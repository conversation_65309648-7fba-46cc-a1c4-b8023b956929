[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(175) "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n  ["params"]: array(0) {\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(27) "autobooks_navigation as nav"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name AS<PERSON>","params":[],"timestamp":"2025-08-18 15:44:53","table":"autobooks_navigation as nav"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n         1: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n         1: []\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(176) "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `user_id` = :where_user_id_1 LIMIT 1"\n  ["params"]: array(2) {\n    [":where_table_name_0"]: string(17) "autodesk_products"\n    [":where_user_id_1"]: int(2)\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `user_id` = :where_user_id_1 LIMIT 1","params":{":where_table_name_0":"autodesk_products",":where_user_id_1":2},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `user_id` = :where_user_id_1 LIMIT 1"\n         1: {":where_table_name_0":"autodesk_products",":where_user_id_1":2}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `user_id` = :where_user_id_1 LIMIT 1"\n         1: {":where_table_name_0":"autodesk_products",":where_user_id_1":2}\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(120) "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n  ["params"]: array(0) {\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'","params":[],"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n         1: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n         1: []\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(124) "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n  ["params"]: array(0) {\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'","params":[],"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n         1: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n         1: []\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(69) "SELECT * FROM autobooks_data_sources WHERE `id` = :where_id_0 LIMIT 1"\n  ["params"]: array(1) {\n    [":where_id_0"]: int(63)\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(22) "autobooks_data_sources"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT * FROM autobooks_data_sources WHERE `id` = :where_id_0 LIMIT 1","params":{":where_id_0":63},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_sources"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_sources WHERE `id` = :where_id_0 LIMIT 1"\n         1: {":where_id_0":63}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_sources WHERE `id` = :where_id_0 LIMIT 1"\n         1: {":where_id_0":63}\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(49) "SELECT * FROM `products_autodesk_catalog` LIMIT 1"\n  ["params"]: array(0) {\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(22) "autobooks_data_sources"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT * FROM `products_autodesk_catalog` LIMIT 1","params":[],"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_sources"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM `products_autodesk_catalog` LIMIT 1"\n         1: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM `products_autodesk_catalog` LIMIT 1"\n         1: []\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(41) "SELECT * FROM `products_autodesk_catalog`"\n  ["params"]: array(0) {\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(22) "autobooks_data_sources"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT * FROM `products_autodesk_catalog`","params":[],"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_sources"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM `products_autodesk_catalog`"\n         1: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM `products_autodesk_catalog`"\n         1: []\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(169) "SELECT * FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 OR `table_name` = :where_table_name_1 OR `data_source_id` = :where_data_source_id_2 LIMIT 1"\n  ["params"]: array(3) {\n    [":where_user_id_0"]: int(2)\n    [":where_table_name_1"]: string(17) "autodesk_products"\n    [":where_data_source_id_2"]: int(63)\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT * FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 OR `table_name` = :where_table_name_1 OR `data_source_id` = :where_data_source_id_2 LIMIT 1","params":{":where_user_id_0":2,":where_table_name_1":"autodesk_products",":where_data_source_id_2":63},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 OR `table_name` = :where_table_name_1 OR `data_source_id` = :where_data_source_id_2 LIMIT 1"\n         1: {":where_user_id_0":2,":where_table_name_1":"autodesk_products",":where_data_source_id_2":63}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 OR `table_name` = :where_table_name_1 OR `data_source_id` = :where_data_source_id_2 LIMIT 1"\n         1: {":where_user_id_0":2,":where_table_name_1":"autodesk_products",":where_data_source_id_2":63}\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(205) "UPDATE autobooks_data_table_storage SET `configuration` = :set_configuration, `updated_at` = :set_updated_at, `table_name` = :set_table_name, `data_source_id` = :set_data_source_id WHERE `id` = :where_id_0"\n  ["params"]: array(5) {\n    [":set_configuration"]: string(13954) "{"structure":[{"id":"col_0_82f043ee7127e7d5d23cc91ebbc5e26d","label":"Unique Hash","field":"unique_hash","filter":true,"fields":["unique_hash"],"visible":true},{"id":"col_1_a62d3a3e711ed6ac93df1562f96a952b","label":"Hash String","field":"hash_string","filter":true,"fields":["hash_string"],"visible":true},{"id":"col_2_4c5668167885bf8500439825c10c1cad","label":"OfferingName","field":"offeringName","filter":true,"fields":["offeringName"],"visible":true},{"id":"col_3_999609054aa657925a5d027e40ba4798","label":"OfferingCode","field":"offeringCode","filter":true,"fields":["offeringCode"],"visible":true},{"id":"col_4_705b8e0aea7f809b7b64c8b982cd888c","label":"OfferingId","field":"offeringId","filter":true,"fields":["offeringId"],"visible":true},{"id":"col_5_f5ea461ed2b96a9258aea9b862a65d97","label":"IntendedUsage Code","field":"intendedUsage_code","filter":true,"fields":["intendedUsage_code"],"visible":true},{"id":"col_6_fa3c14c0ab91af3cf8681adc118caa5e","label":"IntendedUsage Description","field":"intendedUsage_description","filter":true,"fields":["intendedUsage_description"],"visible":true},{"id":"col_7_1dbfb384b9324ba80b1e1ee86efde582","label":"AccessModel Code","field":"accessModel_code","filter":true,"fields":["accessModel_code"],"visible":true},{"id":"col_8_f24904e8e28164f1737337f3d1a4cd39","label":"AccessModel Description","field":"accessModel_description","filter":true,"fields":["accessModel_description"],"visible":true},{"id":"col_9_081246008528e5e66e861b4be40e46c1","label":"ServicePlan Code","field":"servicePlan_code","filter":true,"fields":["servicePlan_code"],"visible":true},{"id":"col_10_be1efbd7fa388834c65459649cb9ed83","label":"ServicePlan Description","field":"servicePlan_description","filter":true,"fields":["servicePlan_description"],"visible":true},{"id":"col_11_8e86dc40cc92baaae006690354e0c55a","label":"Connectivity Code","field":"connectivity_code","filter":true,"fields":["connectivity_code"],"visible":true},{"id":"col_12_74fcc3f307f285e1b2c87469a9fbea88","label":"Connectivity Description","field":"connectivity_description","filter":true,"fields":["connectivity_description"],"visible":true},{"id":"col_13_2c0d9b84f228287239129348a73e0500","label":"Term Code","field":"term_code","filter":true,"fields":["term_code"],"visible":true},{"id":"col_14_3aff2796e8cd499d936c75b7f602a1a3","label":"Term Description","field":"term_description","filter":true,"fields":["term_description"],"visible":true},{"id":"col_15_677e8ed7a86a4a01a69a9b2d3deb41cb","label":"LifeCycleState","field":"lifeCycleState","filter":true,"fields":["lifeCycleState"],"visible":true},{"id":"col_16_1dd60996e7ef4cc0b5eeb8082b784fcd","label":"RenewOnlyDate","field":"renewOnlyDate","filter":true,"fields":["renewOnlyDate"],"visible":true},{"id":"col_17_0e1981da9b4dab6b112791274a8fcbb1","label":"DiscontinueDate","field":"discontinueDate","filter":true,"fields":["discontinueDate"],"visible":true},{"id":"col_18_ad36ecc126d11a0822e1ea6a612ecf22","label":"OrderAction","field":"orderAction","filter":true,"fields":["orderAction"],"visible":true},{"id":"col_19_68e216ee4cadca92f87189136091dffc","label":"SpecialProgramDiscount Code","field":"specialProgramDiscount_code","filter":true,"fields":["specialProgramDiscount_code"],"visible":true},{"id":"col_20_1d28deccfcb849d8700eaa730294a80a","label":"SpecialProgramDiscount Description","field":"specialProgramDiscount_description","filter":true,"fields":["specialProgramDiscount_description"],"visible":true},{"id":"col_21_9fb35d4ab3a0d10ba25b3fbc3be26d3e","label":"FromQty","field":"fromQty","filter":true,"fields":["fromQty"],"visible":true},{"id":"col_22_6f0efddf95d66def82254e208761d507","label":"ToQty","field":"toQty","filter":true,"fields":["toQty"],"visible":true},{"id":"col_23_1af0389838508d7016a9841eb6273962","label":"Currency","field":"currency","filter":true,"fields":["currency"],"visible":true},{"id":"col_24_fdd51ddf7583a729a503d339b1e05364","label":"SRP","field":"SRP","filter":true,"fields":["SRP"],"visible":true},{"id":"col_25_1301694babf4d50d33c0105a88f3dced","label":"CostAfterSpecialProgramDiscount","field":"costAfterSpecialProgramDiscount","filter":true,"fields":["costAfterSpecialProgramDiscount"],"visible":true},{"id":"col_26_66dd7f4db6416c62606f129303a26c7a","label":"RenewalDiscountPercent","field":"renewalDiscountPercent","filter":true,"fields":["renewalDiscountPercent"],"visible":true},{"id":"col_27_d471f8556ece21ccbc8f463456a759ac","label":"RenewalDiscountAmount","field":"renewalDiscountAmount","filter":true,"fields":["renewalDiscountAmount"],"visible":true},{"id":"col_28_996918201154fceac3686e17cfdb7123","label":"CostAfterRenewalDiscount","field":"costAfterRenewalDiscount","filter":true,"fields":["costAfterRenewalDiscount"],"visible":true},{"id":"col_29_af94a3e5bac4d68174010029cd0f289c","label":"TransactionVolumeDiscountPercent","field":"transactionVolumeDiscountPercent","filter":true,"fields":["transactionVolumeDiscountPercent"],"visible":true},{"id":"col_30_d06f6b20f2d588a97bb5c355535b8ce4","label":"TransactionVolumeDiscountAmount","field":"transactionVolumeDiscountAmount","filter":true,"fields":["transactionVolumeDiscountAmount"],"visible":true},{"id":"col_31_1df16f13d9f5890ae574285588616a39","label":"CostAfterTransactionVolumeDiscount","field":"costAfterTransactionVolumeDiscount","filter":true,"fields":["costAfterTransactionVolumeDiscount"],"visible":true},{"id":"col_32_71622f3b0c5ad40e25a10e7ff0fd90d3","label":"ServiceDurationDiscountPercent","field":"serviceDurationDiscountPercent","filter":true,"fields":["serviceDurationDiscountPercent"],"visible":true},{"id":"col_33_815dcd28d4f671cead2da2039499cb27","label":"ServiceDurationDiscountAmount","field":"serviceDurationDiscountAmount","filter":true,"fields":["serviceDurationDiscountAmount"],"visible":true},{"id":"col_34_41c6f4fe10e151e496511d1e1233757c","label":"CostAfterServiceDurationDiscount","field":"costAfterServiceDurationDiscount","filter":true,"fields":["costAfterServiceDurationDiscount"],"visible":true},{"id":"col_35_f09c9dffb716528c44c97e86e9f5a5b0","label":"EffectiveStartDate","field":"effectiveStartDate","filter":true,"fields":["effectiveStartDate"],"visible":true},{"id":"col_36_89a1b222e53cc31b2c4909e4b9cc67b6","label":"EffectiveEndDate","field":"effectiveEndDate","filter":true,"fields":["effectiveEndDate"],"visible":true},{"id":"col_37_c33b4e2749b27eacb05cf8ec70866f17","label":"ChangeFlag","field":"changeFlag","filter":true,"fields":["changeFlag"],"visible":true},{"id":"col_38_ad78e74e6c1cf574bdeb3f261682009a","label":"Last Change","field":"last_change","filter":true,"fields":["last_change"],"visible":true},{"id":"col_39_54870b0544e3d66e1d3bc30dea6d0600","label":"Last Modified","field":"last_modified","filter":true,"fields":["last_modified"],"visible":true},{"id":"col_40_fde81f1177b1541755b672c9dc97315a","label":"Created At","field":"created_at","filter":true,"fields":["created_at"],"visible":true}],"hidden":[],"columns":[{"id":"col_0_82f043ee7127e7d5d23cc91ebbc5e26d","label":"Unique Hash","field":"unique_hash","filter":true,"fields":["unique_hash"],"visible":true},{"id":"col_1_a62d3a3e711ed6ac93df1562f96a952b","label":"Hash String","field":"hash_string","filter":true,"fields":["hash_string"],"visible":true},{"id":"col_2_4c5668167885bf8500439825c10c1cad","label":"OfferingName","field":"offeringName","filter":true,"fields":["offeringName"],"visible":true},{"id":"col_3_999609054aa657925a5d027e40ba4798","label":"OfferingCode","field":"offeringCode","filter":true,"fields":["offeringCode"],"visible":true},{"id":"col_4_705b8e0aea7f809b7b64c8b982cd888c","label":"OfferingId","field":"offeringId","filter":true,"fields":["offeringId"],"visible":true},{"id":"col_5_f5ea461ed2b96a9258aea9b862a65d97","label":"IntendedUsage Code","field":"intendedUsage_code","filter":true,"fields":["intendedUsage_code"],"visible":true},{"id":"col_6_fa3c14c0ab91af3cf8681adc118caa5e","label":"IntendedUsage Description","field":"intendedUsage_description","filter":true,"fields":["intendedUsage_description"],"visible":true},{"id":"col_7_1dbfb384b9324ba80b1e1ee86efde582","label":"AccessModel Code","field":"accessModel_code","filter":true,"fields":["accessModel_code"],"visible":true},{"id":"col_8_f24904e8e28164f1737337f3d1a4cd39","label":"AccessModel Description","field":"accessModel_description","filter":true,"fields":["accessModel_description"],"visible":true},{"id":"col_9_081246008528e5e66e861b4be40e46c1","label":"ServicePlan Code","field":"servicePlan_code","filter":true,"fields":["servicePlan_code"],"visible":true},{"id":"col_10_be1efbd7fa388834c65459649cb9ed83","label":"ServicePlan Description","field":"servicePlan_description","filter":true,"fields":["servicePlan_description"],"visible":true},{"id":"col_11_8e86dc40cc92baaae006690354e0c55a","label":"Connectivity Code","field":"connectivity_code","filter":true,"fields":["connectivity_code"],"visible":true},{"id":"col_12_74fcc3f307f285e1b2c87469a9fbea88","label":"Connectivity Description","field":"connectivity_description","filter":true,"fields":["connectivity_description"],"visible":true},{"id":"col_13_2c0d9b84f228287239129348a73e0500","label":"Term Code","field":"term_code","filter":true,"fields":["term_code"],"visible":true},{"id":"col_14_3aff2796e8cd499d936c75b7f602a1a3","label":"Term Description","field":"term_description","filter":true,"fields":["term_description"],"visible":true},{"id":"col_15_677e8ed7a86a4a01a69a9b2d3deb41cb","label":"LifeCycleState","field":"lifeCycleState","filter":true,"fields":["lifeCycleState"],"visible":true},{"id":"col_16_1dd60996e7ef4cc0b5eeb8082b784fcd","label":"RenewOnlyDate","field":"renewOnlyDate","filter":true,"fields":["renewOnlyDate"],"visible":true},{"id":"col_17_0e1981da9b4dab6b112791274a8fcbb1","label":"DiscontinueDate","field":"discontinueDate","filter":true,"fields":["discontinueDate"],"visible":true},{"id":"col_18_ad36ecc126d11a0822e1ea6a612ecf22","label":"OrderAction","field":"orderAction","filter":true,"fields":["orderAction"],"visible":true},{"id":"col_19_68e216ee4cadca92f87189136091dffc","label":"SpecialProgramDiscount Code","field":"specialProgramDiscount_code","filter":true,"fields":["specialProgramDiscount_code"],"visible":true},{"id":"col_20_1d28deccfcb849d8700eaa730294a80a","label":"SpecialProgramDiscount Description","field":"specialProgramDiscount_description","filter":true,"fields":["specialProgramDiscount_description"],"visible":true},{"id":"col_21_9fb35d4ab3a0d10ba25b3fbc3be26d3e","label":"FromQty","field":"fromQty","filter":true,"fields":["fromQty"],"visible":true},{"id":"col_22_6f0efddf95d66def82254e208761d507","label":"ToQty","field":"toQty","filter":true,"fields":["toQty"],"visible":true},{"id":"col_23_1af0389838508d7016a9841eb6273962","label":"Currency","field":"currency","filter":true,"fields":["currency"],"visible":true},{"id":"col_24_fdd51ddf7583a729a503d339b1e05364","label":"SRP","field":"SRP","filter":true,"fields":["SRP"],"visible":true},{"id":"col_25_1301694babf4d50d33c0105a88f3dced","label":"CostAfterSpecialProgramDiscount","field":"costAfterSpecialProgramDiscount","filter":true,"fields":["costAfterSpecialProgramDiscount"],"visible":true},{"id":"col_26_66dd7f4db6416c62606f129303a26c7a","label":"RenewalDiscountPercent","field":"renewalDiscountPercent","filter":true,"fields":["renewalDiscountPercent"],"visible":true},{"id":"col_27_d471f8556ece21ccbc8f463456a759ac","label":"RenewalDiscountAmount","field":"renewalDiscountAmount","filter":true,"fields":["renewalDiscountAmount"],"visible":true},{"id":"col_28_996918201154fceac3686e17cfdb7123","label":"CostAfterRenewalDiscount","field":"costAfterRenewalDiscount","filter":true,"fields":["costAfterRenewalDiscount"],"visible":true},{"id":"col_29_af94a3e5bac4d68174010029cd0f289c","label":"TransactionVolumeDiscountPercent","field":"transactionVolumeDiscountPercent","filter":true,"fields":["transactionVolumeDiscountPercent"],"visible":true},{"id":"col_30_d06f6b20f2d588a97bb5c355535b8ce4","label":"TransactionVolumeDiscountAmount","field":"transactionVolumeDiscountAmount","filter":true,"fields":["transactionVolumeDiscountAmount"],"visible":true},{"id":"col_31_1df16f13d9f5890ae574285588616a39","label":"CostAfterTransactionVolumeDiscount","field":"costAfterTransactionVolumeDiscount","filter":true,"fields":["costAfterTransactionVolumeDiscount"],"visible":true},{"id":"col_32_71622f3b0c5ad40e25a10e7ff0fd90d3","label":"ServiceDurationDiscountPercent","field":"serviceDurationDiscountPercent","filter":true,"fields":["serviceDurationDiscountPercent"],"visible":true},{"id":"col_33_815dcd28d4f671cead2da2039499cb27","label":"ServiceDurationDiscountAmount","field":"serviceDurationDiscountAmount","filter":true,"fields":["serviceDurationDiscountAmount"],"visible":true},{"id":"col_34_41c6f4fe10e151e496511d1e1233757c","label":"CostAfterServiceDurationDiscount","field":"costAfterServiceDurationDiscount","filter":true,"fields":["costAfterServiceDurationDiscount"],"visible":true},{"id":"col_35_f09c9dffb716528c44c97e86e9f5a5b0","label":"EffectiveStartDate","field":"effectiveStartDate","filter":true,"fields":["effectiveStartDate"],"visible":true},{"id":"col_36_89a1b222e53cc31b2c4909e4b9cc67b6","label":"EffectiveEndDate","field":"effectiveEndDate","filter":true,"fields":["effectiveEndDate"],"visible":true},{"id":"col_37_c33b4e2749b27eacb05cf8ec70866f17","label":"ChangeFlag","field":"changeFlag","filter":true,"fields":["changeFlag"],"visible":true},{"id":"col_38_ad78e74e6c1cf574bdeb3f261682009a","label":"Last Change","field":"last_change","filter":true,"fields":["last_change"],"visible":true},{"id":"col_39_54870b0544e3d66e1d3bc30dea6d0600","label":"Last Modified","field":"last_modified","filter":true,"fields":["last_modified"],"visible":true},{"id":"col_40_fde81f1177b1541755b672c9dc97315a","label":"Created At","field":"created_at","filter":true,"fields":["created_at"],"visible":true}],"created_at":"2025-08-18 14:20:46","data_source_type":"data_source","data_source_id":63,"updated_at":"2025-08-18 15:44:53"}"\n    [":set_updated_at"]: string(19) "2025-08-18 15:44:53"\n    [":set_table_name"]: string(17) "autodesk_products"\n    [":set_data_source_id"]: int(63)\n    [":where_id_0"]: int(2)\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"UPDATE autobooks_data_table_storage SET `configuration` = :set_configuration, `updated_at` = :set_updated_at, `table_name` = :set_table_name, `data_source_id` = :set_data_source_id WHERE `id` = :where_id_0","params":{":set_configuration":"{\"structure\":[{\"id\":\"col_0_82f043ee7127e7d5d23cc91ebbc5e26d\",\"label\":\"Unique Hash\",\"field\":\"unique_hash\",\"filter\":true,\"fields\":[\"unique_hash\"],\"visible\":true},{\"id\":\"col_1_a62d3a3e711ed6ac93df1562f96a952b\",\"label\":\"Hash String\",\"field\":\"hash_string\",\"filter\":true,\"fields\":[\"hash_string\"],\"visible\":true},{\"id\":\"col_2_4c5668167885bf8500439825c10c1cad\",\"label\":\"OfferingName\",\"field\":\"offeringName\",\"filter\":true,\"fields\":[\"offeringName\"],\"visible\":true},{\"id\":\"col_3_999609054aa657925a5d027e40ba4798\",\"label\":\"OfferingCode\",\"field\":\"offeringCode\",\"filter\":true,\"fields\":[\"offeringCode\"],\"visible\":true},{\"id\":\"col_4_705b8e0aea7f809b7b64c8b982cd888c\",\"label\":\"OfferingId\",\"field\":\"offeringId\",\"filter\":true,\"fields\":[\"offeringId\"],\"visible\":true},{\"id\":\"col_5_f5ea461ed2b96a9258aea9b862a65d97\",\"label\":\"IntendedUsage Code\",\"field\":\"intendedUsage_code\",\"filter\":true,\"fields\":[\"intendedUsage_code\"],\"visible\":true},{\"id\":\"col_6_fa3c14c0ab91af3cf8681adc118caa5e\",\"label\":\"IntendedUsage Description\",\"field\":\"intendedUsage_description\",\"filter\":true,\"fields\":[\"intendedUsage_description\"],\"visible\":true},{\"id\":\"col_7_1dbfb384b9324ba80b1e1ee86efde582\",\"label\":\"AccessModel Code\",\"field\":\"accessModel_code\",\"filter\":true,\"fields\":[\"accessModel_code\"],\"visible\":true},{\"id\":\"col_8_f24904e8e28164f1737337f3d1a4cd39\",\"label\":\"AccessModel Description\",\"field\":\"accessModel_description\",\"filter\":true,\"fields\":[\"accessModel_description\"],\"visible\":true},{\"id\":\"col_9_081246008528e5e66e861b4be40e46c1\",\"label\":\"ServicePlan Code\",\"field\":\"servicePlan_code\",\"filter\":true,\"fields\":[\"servicePlan_code\"],\"visible\":true},{\"id\":\"col_10_be1efbd7fa388834c65459649cb9ed83\",\"label\":\"ServicePlan Description\",\"field\":\"servicePlan_description\",\"filter\":true,\"fields\":[\"servicePlan_description\"],\"visible\":true},{\"id\":\"col_11_8e86dc40cc92baaae006690354e0c55a\",\"label\":\"Connectivity Code\",\"field\":\"connectivity_code\",\"filter\":true,\"fields\":[\"connectivity_code\"],\"visible\":true},{\"id\":\"col_12_74fcc3f307f285e1b2c87469a9fbea88\",\"label\":\"Connectivity Description\",\"field\":\"connectivity_description\",\"filter\":true,\"fields\":[\"connectivity_description\"],\"visible\":true},{\"id\":\"col_13_2c0d9b84f228287239129348a73e0500\",\"label\":\"Term Code\",\"field\":\"term_code\",\"filter\":true,\"fields\":[\"term_code\"],\"visible\":true},{\"id\":\"col_14_3aff2796e8cd499d936c75b7f602a1a3\",\"label\":\"Term Description\",\"field\":\"term_description\",\"filter\":true,\"fields\":[\"term_description\"],\"visible\":true},{\"id\":\"col_15_677e8ed7a86a4a01a69a9b2d3deb41cb\",\"label\":\"LifeCycleState\",\"field\":\"lifeCycleState\",\"filter\":true,\"fields\":[\"lifeCycleState\"],\"visible\":true},{\"id\":\"col_16_1dd60996e7ef4cc0b5eeb8082b784fcd\",\"label\":\"RenewOnlyDate\",\"field\":\"renewOnlyDate\",\"filter\":true,\"fields\":[\"renewOnlyDate\"],\"visible\":true},{\"id\":\"col_17_0e1981da9b4dab6b112791274a8fcbb1\",\"label\":\"DiscontinueDate\",\"field\":\"discontinueDate\",\"filter\":true,\"fields\":[\"discontinueDate\"],\"visible\":true},{\"id\":\"col_18_ad36ecc126d11a0822e1ea6a612ecf22\",\"label\":\"OrderAction\",\"field\":\"orderAction\",\"filter\":true,\"fields\":[\"orderAction\"],\"visible\":true},{\"id\":\"col_19_68e216ee4cadca92f87189136091dffc\",\"label\":\"SpecialProgramDiscount Code\",\"field\":\"specialProgramDiscount_code\",\"filter\":true,\"fields\":[\"specialProgramDiscount_code\"],\"visible\":true},{\"id\":\"col_20_1d28deccfcb849d8700eaa730294a80a\",\"label\":\"SpecialProgramDiscount Description\",\"field\":\"specialProgramDiscount_description\",\"filter\":true,\"fields\":[\"specialProgramDiscount_description\"],\"visible\":true},{\"id\":\"col_21_9fb35d4ab3a0d10ba25b3fbc3be26d3e\",\"label\":\"FromQty\",\"field\":\"fromQty\",\"filter\":true,\"fields\":[\"fromQty\"],\"visible\":true},{\"id\":\"col_22_6f0efddf95d66def82254e208761d507\",\"label\":\"ToQty\",\"field\":\"toQty\",\"filter\":true,\"fields\":[\"toQty\"],\"visible\":true},{\"id\":\"col_23_1af0389838508d7016a9841eb6273962\",\"label\":\"Currency\",\"field\":\"currency\",\"filter\":true,\"fields\":[\"currency\"],\"visible\":true},{\"id\":\"col_24_fdd51ddf7583a729a503d339b1e05364\",\"label\":\"SRP\",\"field\":\"SRP\",\"filter\":true,\"fields\":[\"SRP\"],\"visible\":true},{\"id\":\"col_25_1301694babf4d50d33c0105a88f3dced\",\"label\":\"CostAfterSpecialProgramDiscount\",\"field\":\"costAfterSpecialProgramDiscount\",\"filter\":true,\"fields\":[\"costAfterSpecialProgramDiscount\"],\"visible\":true},{\"id\":\"col_26_66dd7f4db6416c62606f129303a26c7a\",\"label\":\"RenewalDiscountPercent\",\"field\":\"renewalDiscountPercent\",\"filter\":true,\"fields\":[\"renewalDiscountPercent\"],\"visible\":true},{\"id\":\"col_27_d471f8556ece21ccbc8f463456a759ac\",\"label\":\"RenewalDiscountAmount\",\"field\":\"renewalDiscountAmount\",\"filter\":true,\"fields\":[\"renewalDiscountAmount\"],\"visible\":true},{\"id\":\"col_28_996918201154fceac3686e17cfdb7123\",\"label\":\"CostAfterRenewalDiscount\",\"field\":\"costAfterRenewalDiscount\",\"filter\":true,\"fields\":[\"costAfterRenewalDiscount\"],\"visible\":true},{\"id\":\"col_29_af94a3e5bac4d68174010029cd0f289c\",\"label\":\"TransactionVolumeDiscountPercent\",\"field\":\"transactionVolumeDiscountPercent\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountPercent\"],\"visible\":true},{\"id\":\"col_30_d06f6b20f2d588a97bb5c355535b8ce4\",\"label\":\"TransactionVolumeDiscountAmount\",\"field\":\"transactionVolumeDiscountAmount\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountAmount\"],\"visible\":true},{\"id\":\"col_31_1df16f13d9f5890ae574285588616a39\",\"label\":\"CostAfterTransactionVolumeDiscount\",\"field\":\"costAfterTransactionVolumeDiscount\",\"filter\":true,\"fields\":[\"costAfterTransactionVolumeDiscount\"],\"visible\":true},{\"id\":\"col_32_71622f3b0c5ad40e25a10e7ff0fd90d3\",\"label\":\"ServiceDurationDiscountPercent\",\"field\":\"serviceDurationDiscountPercent\",\"filter\":true,\"fields\":[\"serviceDurationDiscountPercent\"],\"visible\":true},{\"id\":\"col_33_815dcd28d4f671cead2da2039499cb27\",\"label\":\"ServiceDurationDiscountAmount\",\"field\":\"serviceDurationDiscountAmount\",\"filter\":true,\"fields\":[\"serviceDurationDiscountAmount\"],\"visible\":true},{\"id\":\"col_34_41c6f4fe10e151e496511d1e1233757c\",\"label\":\"CostAfterServiceDurationDiscount\",\"field\":\"costAfterServiceDurationDiscount\",\"filter\":true,\"fields\":[\"costAfterServiceDurationDiscount\"],\"visible\":true},{\"id\":\"col_35_f09c9dffb716528c44c97e86e9f5a5b0\",\"label\":\"EffectiveStartDate\",\"field\":\"effectiveStartDate\",\"filter\":true,\"fields\":[\"effectiveStartDate\"],\"visible\":true},{\"id\":\"col_36_89a1b222e53cc31b2c4909e4b9cc67b6\",\"label\":\"EffectiveEndDate\",\"field\":\"effectiveEndDate\",\"filter\":true,\"fields\":[\"effectiveEndDate\"],\"visible\":true},{\"id\":\"col_37_c33b4e2749b27eacb05cf8ec70866f17\",\"label\":\"ChangeFlag\",\"field\":\"changeFlag\",\"filter\":true,\"fields\":[\"changeFlag\"],\"visible\":true},{\"id\":\"col_38_ad78e74e6c1cf574bdeb3f261682009a\",\"label\":\"Last Change\",\"field\":\"last_change\",\"filter\":true,\"fields\":[\"last_change\"],\"visible\":true},{\"id\":\"col_39_54870b0544e3d66e1d3bc30dea6d0600\",\"label\":\"Last Modified\",\"field\":\"last_modified\",\"filter\":true,\"fields\":[\"last_modified\"],\"visible\":true},{\"id\":\"col_40_fde81f1177b1541755b672c9dc97315a\",\"label\":\"Created At\",\"field\":\"created_at\",\"filter\":true,\"fields\":[\"created_at\"],\"visible\":true}],\"hidden\":[],\"columns\":[{\"id\":\"col_0_82f043ee7127e7d5d23cc91ebbc5e26d\",\"label\":\"Unique Hash\",\"field\":\"unique_hash\",\"filter\":true,\"fields\":[\"unique_hash\"],\"visible\":true},{\"id\":\"col_1_a62d3a3e711ed6ac93df1562f96a952b\",\"label\":\"Hash String\",\"field\":\"hash_string\",\"filter\":true,\"fields\":[\"hash_string\"],\"visible\":true},{\"id\":\"col_2_4c5668167885bf8500439825c10c1cad\",\"label\":\"OfferingName\",\"field\":\"offeringName\",\"filter\":true,\"fields\":[\"offeringName\"],\"visible\":true},{\"id\":\"col_3_999609054aa657925a5d027e40ba4798\",\"label\":\"OfferingCode\",\"field\":\"offeringCode\",\"filter\":true,\"fields\":[\"offeringCode\"],\"visible\":true},{\"id\":\"col_4_705b8e0aea7f809b7b64c8b982cd888c\",\"label\":\"OfferingId\",\"field\":\"offeringId\",\"filter\":true,\"fields\":[\"offeringId\"],\"visible\":true},{\"id\":\"col_5_f5ea461ed2b96a9258aea9b862a65d97\",\"label\":\"IntendedUsage Code\",\"field\":\"intendedUsage_code\",\"filter\":true,\"fields\":[\"intendedUsage_code\"],\"visible\":true},{\"id\":\"col_6_fa3c14c0ab91af3cf8681adc118caa5e\",\"label\":\"IntendedUsage Description\",\"field\":\"intendedUsage_description\",\"filter\":true,\"fields\":[\"intendedUsage_description\"],\"visible\":true},{\"id\":\"col_7_1dbfb384b9324ba80b1e1ee86efde582\",\"label\":\"AccessModel Code\",\"field\":\"accessModel_code\",\"filter\":true,\"fields\":[\"accessModel_code\"],\"visible\":true},{\"id\":\"col_8_f24904e8e28164f1737337f3d1a4cd39\",\"label\":\"AccessModel Description\",\"field\":\"accessModel_description\",\"filter\":true,\"fields\":[\"accessModel_description\"],\"visible\":true},{\"id\":\"col_9_081246008528e5e66e861b4be40e46c1\",\"label\":\"ServicePlan Code\",\"field\":\"servicePlan_code\",\"filter\":true,\"fields\":[\"servicePlan_code\"],\"visible\":true},{\"id\":\"col_10_be1efbd7fa388834c65459649cb9ed83\",\"label\":\"ServicePlan Description\",\"field\":\"servicePlan_description\",\"filter\":true,\"fields\":[\"servicePlan_description\"],\"visible\":true},{\"id\":\"col_11_8e86dc40cc92baaae006690354e0c55a\",\"label\":\"Connectivity Code\",\"field\":\"connectivity_code\",\"filter\":true,\"fields\":[\"connectivity_code\"],\"visible\":true},{\"id\":\"col_12_74fcc3f307f285e1b2c87469a9fbea88\",\"label\":\"Connectivity Description\",\"field\":\"connectivity_description\",\"filter\":true,\"fields\":[\"connectivity_description\"],\"visible\":true},{\"id\":\"col_13_2c0d9b84f228287239129348a73e0500\",\"label\":\"Term Code\",\"field\":\"term_code\",\"filter\":true,\"fields\":[\"term_code\"],\"visible\":true},{\"id\":\"col_14_3aff2796e8cd499d936c75b7f602a1a3\",\"label\":\"Term Description\",\"field\":\"term_description\",\"filter\":true,\"fields\":[\"term_description\"],\"visible\":true},{\"id\":\"col_15_677e8ed7a86a4a01a69a9b2d3deb41cb\",\"label\":\"LifeCycleState\",\"field\":\"lifeCycleState\",\"filter\":true,\"fields\":[\"lifeCycleState\"],\"visible\":true},{\"id\":\"col_16_1dd60996e7ef4cc0b5eeb8082b784fcd\",\"label\":\"RenewOnlyDate\",\"field\":\"renewOnlyDate\",\"filter\":true,\"fields\":[\"renewOnlyDate\"],\"visible\":true},{\"id\":\"col_17_0e1981da9b4dab6b112791274a8fcbb1\",\"label\":\"DiscontinueDate\",\"field\":\"discontinueDate\",\"filter\":true,\"fields\":[\"discontinueDate\"],\"visible\":true},{\"id\":\"col_18_ad36ecc126d11a0822e1ea6a612ecf22\",\"label\":\"OrderAction\",\"field\":\"orderAction\",\"filter\":true,\"fields\":[\"orderAction\"],\"visible\":true},{\"id\":\"col_19_68e216ee4cadca92f87189136091dffc\",\"label\":\"SpecialProgramDiscount Code\",\"field\":\"specialProgramDiscount_code\",\"filter\":true,\"fields\":[\"specialProgramDiscount_code\"],\"visible\":true},{\"id\":\"col_20_1d28deccfcb849d8700eaa730294a80a\",\"label\":\"SpecialProgramDiscount Description\",\"field\":\"specialProgramDiscount_description\",\"filter\":true,\"fields\":[\"specialProgramDiscount_description\"],\"visible\":true},{\"id\":\"col_21_9fb35d4ab3a0d10ba25b3fbc3be26d3e\",\"label\":\"FromQty\",\"field\":\"fromQty\",\"filter\":true,\"fields\":[\"fromQty\"],\"visible\":true},{\"id\":\"col_22_6f0efddf95d66def82254e208761d507\",\"label\":\"ToQty\",\"field\":\"toQty\",\"filter\":true,\"fields\":[\"toQty\"],\"visible\":true},{\"id\":\"col_23_1af0389838508d7016a9841eb6273962\",\"label\":\"Currency\",\"field\":\"currency\",\"filter\":true,\"fields\":[\"currency\"],\"visible\":true},{\"id\":\"col_24_fdd51ddf7583a729a503d339b1e05364\",\"label\":\"SRP\",\"field\":\"SRP\",\"filter\":true,\"fields\":[\"SRP\"],\"visible\":true},{\"id\":\"col_25_1301694babf4d50d33c0105a88f3dced\",\"label\":\"CostAfterSpecialProgramDiscount\",\"field\":\"costAfterSpecialProgramDiscount\",\"filter\":true,\"fields\":[\"costAfterSpecialProgramDiscount\"],\"visible\":true},{\"id\":\"col_26_66dd7f4db6416c62606f129303a26c7a\",\"label\":\"RenewalDiscountPercent\",\"field\":\"renewalDiscountPercent\",\"filter\":true,\"fields\":[\"renewalDiscountPercent\"],\"visible\":true},{\"id\":\"col_27_d471f8556ece21ccbc8f463456a759ac\",\"label\":\"RenewalDiscountAmount\",\"field\":\"renewalDiscountAmount\",\"filter\":true,\"fields\":[\"renewalDiscountAmount\"],\"visible\":true},{\"id\":\"col_28_996918201154fceac3686e17cfdb7123\",\"label\":\"CostAfterRenewalDiscount\",\"field\":\"costAfterRenewalDiscount\",\"filter\":true,\"fields\":[\"costAfterRenewalDiscount\"],\"visible\":true},{\"id\":\"col_29_af94a3e5bac4d68174010029cd0f289c\",\"label\":\"TransactionVolumeDiscountPercent\",\"field\":\"transactionVolumeDiscountPercent\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountPercent\"],\"visible\":true},{\"id\":\"col_30_d06f6b20f2d588a97bb5c355535b8ce4\",\"label\":\"TransactionVolumeDiscountAmount\",\"field\":\"transactionVolumeDiscountAmount\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountAmount\"],\"visible\":true},{\"id\":\"col_31_1df16f13d9f5890ae574285588616a39\",\"label\":\"CostAfterTransactionVolumeDiscount\",\"field\":\"costAfterTransactionVolumeDiscount\",\"filter\":true,\"fields\":[\"costAfterTransactionVolumeDiscount\"],\"visible\":true},{\"id\":\"col_32_71622f3b0c5ad40e25a10e7ff0fd90d3\",\"label\":\"ServiceDurationDiscountPercent\",\"field\":\"serviceDurationDiscountPercent\",\"filter\":true,\"fields\":[\"serviceDurationDiscountPercent\"],\"visible\":true},{\"id\":\"col_33_815dcd28d4f671cead2da2039499cb27\",\"label\":\"ServiceDurationDiscountAmount\",\"field\":\"serviceDurationDiscountAmount\",\"filter\":true,\"fields\":[\"serviceDurationDiscountAmount\"],\"visible\":true},{\"id\":\"col_34_41c6f4fe10e151e496511d1e1233757c\",\"label\":\"CostAfterServiceDurationDiscount\",\"field\":\"costAfterServiceDurationDiscount\",\"filter\":true,\"fields\":[\"costAfterServiceDurationDiscount\"],\"visible\":true},{\"id\":\"col_35_f09c9dffb716528c44c97e86e9f5a5b0\",\"label\":\"EffectiveStartDate\",\"field\":\"effectiveStartDate\",\"filter\":true,\"fields\":[\"effectiveStartDate\"],\"visible\":true},{\"id\":\"col_36_89a1b222e53cc31b2c4909e4b9cc67b6\",\"label\":\"EffectiveEndDate\",\"field\":\"effectiveEndDate\",\"filter\":true,\"fields\":[\"effectiveEndDate\"],\"visible\":true},{\"id\":\"col_37_c33b4e2749b27eacb05cf8ec70866f17\",\"label\":\"ChangeFlag\",\"field\":\"changeFlag\",\"filter\":true,\"fields\":[\"changeFlag\"],\"visible\":true},{\"id\":\"col_38_ad78e74e6c1cf574bdeb3f261682009a\",\"label\":\"Last Change\",\"field\":\"last_change\",\"filter\":true,\"fields\":[\"last_change\"],\"visible\":true},{\"id\":\"col_39_54870b0544e3d66e1d3bc30dea6d0600\",\"label\":\"Last Modified\",\"field\":\"last_modified\",\"filter\":true,\"fields\":[\"last_modified\"],\"visible\":true},{\"id\":\"col_40_fde81f1177b1541755b672c9dc97315a\",\"label\":\"Created At\",\"field\":\"created_at\",\"filter\":true,\"fields\":[\"created_at\"],\"visible\":true}],\"created_at\":\"2025-08-18 14:20:46\",\"data_source_type\":\"data_source\",\"data_source_id\":63,\"updated_at\":\"2025-08-18 15:44:53\"}",":set_updated_at":"2025-08-18 15:44:53",":set_table_name":"autodesk_products",":set_data_source_id":63,":where_id_0":2},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "UPDATE autobooks_data_table_storage SET `configuration` = :set_configuration, `updated_at` = :set_updated_at, `table_name` = :set_table_name, `data_source_id` = :set_data_source_id WHERE `id` = :where_id_0"\n         1: {":set_configuration":"{\"structure\":[{\"id\":\"col_0_82f043ee7127e7d5d23cc91ebbc5e26d\",\"label\":\"Unique Hash\",\"field\":\"unique_hash\",\"filter\":true,\"fields\":[\"unique_hash\"],\"visible\":true},{\"id\":\"col_1_a62d3a3e711ed6ac93df1562f96a952b\",\"label\":\"Hash String\",\"field\":\"hash_string\",\"filter\":true,\"fields\":[\"hash_string\"],\"visible\":true},{\"id\":\"col_2_4c5668167885bf8500439825c10c1cad\",\"label\":\"OfferingName\",\"field\":\"offeringName\",\"filter\":true,\"fields\":[\"offeringName\"],\"visible\":true},{\"id\":\"col_3_999609054aa657925a5d027e40ba4798\",\"label\":\"OfferingCode\",\"field\":\"offeringCode\",\"filter\":true,\"fields\":[\"offeringCode\"],\"visible\":true},{\"id\":\"col_4_705b8e0aea7f809b7b64c8b982cd888c\",\"label\":\"OfferingId\",\"field\":\"offeringId\",\"filter\":true,\"fields\":[\"offeringId\"],\"visible\":true},{\"id\":\"col_5_f5ea461ed2b96a9258aea9b862a65d97\",\"label\":\"IntendedUsage Code\",\"field\":\"intendedUsage_code\",\"filter\":true,\"fields\":[\"intendedUsage_code\"],\"visible\":true},{\"id\":\"col_6_fa3c14c0ab91af3cf8681adc118caa5e\",\"label\":\"IntendedUsage Description\",\"field\":\"intendedUsage_description\",\"filter\":true,\"fields\":[\"intendedUsage_description\"],\"visible\":true},{\"id\":\"col_7_1dbfb384b9324ba80b1e1ee86efde582\",\"label\":\"AccessModel Code\",\"field\":\"accessModel_code\",\"filter\":true,\"fields\":[\"accessModel_code\"],\"visible\":true},{\"id\":\"col_8_f24904e8e28164f1737337f3d1a4cd39\",\"label\":\"AccessModel Description\",\"field\":\"accessModel_description\",\"filter\":true,\"fields\":[\"accessModel_description\"],\"visible\":true},{\"id\":\"col_9_081246008528e5e66e861b4be40e46c1\",\"label\":\"ServicePlan Code\",\"field\":\"servicePlan_code\",\"filter\":true,\"fields\":[\"servicePlan_code\"],\"visible\":true},{\"id\":\"col_10_be1efbd7fa388834c65459649cb9ed83\",\"label\":\"ServicePlan Description\",\"field\":\"servicePlan_description\",\"filter\":true,\"fields\":[\"servicePlan_description\"],\"visible\":true},{\"id\":\"col_11_8e86dc40cc92baaae006690354e0c55a\",\"label\":\"Connectivity Code\",\"field\":\"connectivity_code\",\"filter\":true,\"fields\":[\"connectivity_code\"],\"visible\":true},{\"id\":\"col_12_74fcc3f307f285e1b2c87469a9fbea88\",\"label\":\"Connectivity Description\",\"field\":\"connectivity_description\",\"filter\":true,\"fields\":[\"connectivity_description\"],\"visible\":true},{\"id\":\"col_13_2c0d9b84f228287239129348a73e0500\",\"label\":\"Term Code\",\"field\":\"term_code\",\"filter\":true,\"fields\":[\"term_code\"],\"visible\":true},{\"id\":\"col_14_3aff2796e8cd499d936c75b7f602a1a3\",\"label\":\"Term Description\",\"field\":\"term_description\",\"filter\":true,\"fields\":[\"term_description\"],\"visible\":true},{\"id\":\"col_15_677e8ed7a86a4a01a69a9b2d3deb41cb\",\"label\":\"LifeCycleState\",\"field\":\"lifeCycleState\",\"filter\":true,\"fields\":[\"lifeCycleState\"],\"visible\":true},{\"id\":\"col_16_1dd60996e7ef4cc0b5eeb8082b784fcd\",\"label\":\"RenewOnlyDate\",\"field\":\"renewOnlyDate\",\"filter\":true,\"fields\":[\"renewOnlyDate\"],\"visible\":true},{\"id\":\"col_17_0e1981da9b4dab6b112791274a8fcbb1\",\"label\":\"DiscontinueDate\",\"field\":\"discontinueDate\",\"filter\":true,\"fields\":[\"discontinueDate\"],\"visible\":true},{\"id\":\"col_18_ad36ecc126d11a0822e1ea6a612ecf22\",\"label\":\"OrderAction\",\"field\":\"orderAction\",\"filter\":true,\"fields\":[\"orderAction\"],\"visible\":true},{\"id\":\"col_19_68e216ee4cadca92f87189136091dffc\",\"label\":\"SpecialProgramDiscount Code\",\"field\":\"specialProgramDiscount_code\",\"filter\":true,\"fields\":[\"specialProgramDiscount_code\"],\"visible\":true},{\"id\":\"col_20_1d28deccfcb849d8700eaa730294a80a\",\"label\":\"SpecialProgramDiscount Description\",\"field\":\"specialProgramDiscount_description\",\"filter\":true,\"fields\":[\"specialProgramDiscount_description\"],\"visible\":true},{\"id\":\"col_21_9fb35d4ab3a0d10ba25b3fbc3be26d3e\",\"label\":\"FromQty\",\"field\":\"fromQty\",\"filter\":true,\"fields\":[\"fromQty\"],\"visible\":true},{\"id\":\"col_22_6f0efddf95d66def82254e208761d507\",\"label\":\"ToQty\",\"field\":\"toQty\",\"filter\":true,\"fields\":[\"toQty\"],\"visible\":true},{\"id\":\"col_23_1af0389838508d7016a9841eb6273962\",\"label\":\"Currency\",\"field\":\"currency\",\"filter\":true,\"fields\":[\"currency\"],\"visible\":true},{\"id\":\"col_24_fdd51ddf7583a729a503d339b1e05364\",\"label\":\"SRP\",\"field\":\"SRP\",\"filter\":true,\"fields\":[\"SRP\"],\"visible\":true},{\"id\":\"col_25_1301694babf4d50d33c0105a88f3dced\",\"label\":\"CostAfterSpecialProgramDiscount\",\"field\":\"costAfterSpecialProgramDiscount\",\"filter\":true,\"fields\":[\"costAfterSpecialProgramDiscount\"],\"visible\":true},{\"id\":\"col_26_66dd7f4db6416c62606f129303a26c7a\",\"label\":\"RenewalDiscountPercent\",\"field\":\"renewalDiscountPercent\",\"filter\":true,\"fields\":[\"renewalDiscountPercent\"],\"visible\":true},{\"id\":\"col_27_d471f8556ece21ccbc8f463456a759ac\",\"label\":\"RenewalDiscountAmount\",\"field\":\"renewalDiscountAmount\",\"filter\":true,\"fields\":[\"renewalDiscountAmount\"],\"visible\":true},{\"id\":\"col_28_996918201154fceac3686e17cfdb7123\",\"label\":\"CostAfterRenewalDiscount\",\"field\":\"costAfterRenewalDiscount\",\"filter\":true,\"fields\":[\"costAfterRenewalDiscount\"],\"visible\":true},{\"id\":\"col_29_af94a3e5bac4d68174010029cd0f289c\",\"label\":\"TransactionVolumeDiscountPercent\",\"field\":\"transactionVolumeDiscountPercent\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountPercent\"],\"visible\":true},{\"id\":\"col_30_d06f6b20f2d588a97bb5c355535b8ce4\",\"label\":\"TransactionVolumeDiscountAmount\",\"field\":\"transactionVolumeDiscountAmount\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountAmount\"],\"visible\":true},{\"id\":\"col_31_1df16f13d9f5890ae574285588616a39\",\"label\":\"CostAfterTransactionVolumeDiscount\",\"field\":\"costAfterTransactionVolumeDiscount\",\"filter\":true,\"fields\":[\"costAfterTransactionVolumeDiscount\"],\"visible\":true},{\"id\":\"col_32_71622f3b0c5ad40e25a10e7ff0fd90d3\",\"label\":\"ServiceDurationDiscountPercent\",\"field\":\"serviceDurationDiscountPercent\",\"filter\":true,\"fields\":[\"serviceDurationDiscountPercent\"],\"visible\":true},{\"id\":\"col_33_815dcd28d4f671cead2da2039499cb27\",\"label\":\"ServiceDurationDiscountAmount\",\"field\":\"serviceDurationDiscountAmount\",\"filter\":true,\"fields\":[\"serviceDurationDiscountAmount\"],\"visible\":true},{\"id\":\"col_34_41c6f4fe10e151e496511d1e1233757c\",\"label\":\"CostAfterServiceDurationDiscount\",\"field\":\"costAfterServiceDurationDiscount\",\"filter\":true,\"fields\":[\"costAfterServiceDurationDiscount\"],\"visible\":true},{\"id\":\"col_35_f09c9dffb716528c44c97e86e9f5a5b0\",\"label\":\"EffectiveStartDate\",\"field\":\"effectiveStartDate\",\"filter\":true,\"fields\":[\"effectiveStartDate\"],\"visible\":true},{\"id\":\"col_36_89a1b222e53cc31b2c4909e4b9cc67b6\",\"label\":\"EffectiveEndDate\",\"field\":\"effectiveEndDate\",\"filter\":true,\"fields\":[\"effectiveEndDate\"],\"visible\":true},{\"id\":\"col_37_c33b4e2749b27eacb05cf8ec70866f17\",\"label\":\"ChangeFlag\",\"field\":\"changeFlag\",\"filter\":true,\"fields\":[\"changeFlag\"],\"visible\":true},{\"id\":\"col_38_ad78e74e6c1cf574bdeb3f261682009a\",\"label\":\"Last Change\",\"field\":\"last_change\",\"filter\":true,\"fields\":[\"last_change\"],\"visible\":true},{\"id\":\"col_39_54870b0544e3d66e1d3bc30dea6d0600\",\"label\":\"Last Modified\",\"field\":\"last_modified\",\"filter\":true,\"fields\":[\"last_modified\"],\"visible\":true},{\"id\":\"col_40_fde81f1177b1541755b672c9dc97315a\",\"label\":\"Created At\",\"field\":\"created_at\",\"filter\":true,\"fields\":[\"created_at\"],\"visible\":true}],\"hidden\":[],\"columns\":[{\"id\":\"col_0_82f043ee7127e7d5d23cc91ebbc5e26d\",\"label\":\"Unique Hash\",\"field\":\"unique_hash\",\"filter\":true,\"fields\":[\"unique_hash\"],\"visible\":true},{\"id\":\"col_1_a62d3a3e711ed6ac93df1562f96a952b\",\"label\":\"Hash String\",\"field\":\"hash_string\",\"filter\":true,\"fields\":[\"hash_string\"],\"visible\":true},{\"id\":\"col_2_4c5668167885bf8500439825c10c1cad\",\"label\":\"OfferingName\",\"field\":\"offeringName\",\"filter\":true,\"fields\":[\"offeringName\"],\"visible\":true},{\"id\":\"col_3_999609054aa657925a5d027e40ba4798\",\"label\":\"OfferingCode\",\"field\":\"offeringCode\",\"filter\":true,\"fields\":[\"offeringCode\"],\"visible\":true},{\"id\":\"col_4_705b8e0aea7f809b7b64c8b982cd888c\",\"label\":\"OfferingId\",\"field\":\"offeringId\",\"filter\":true,\"fields\":[\"offeringId\"],\"visible\":true},{\"id\":\"col_5_f5ea461ed2b96a9258aea9b862a65d97\",\"label\":\"IntendedUsage Code\",\"field\":\"intendedUsage_code\",\"filter\":true,\"fields\":[\"intendedUsage_code\"],\"visible\":true},{\"id\":\"col_6_fa3c14c0ab91af3cf8681adc118caa5e\",\"label\":\"IntendedUsage Description\",\"field\":\"intendedUsage_description\",\"filter\":true,\"fields\":[\"intendedUsage_description\"],\"visible\":true},{\"id\":\"col_7_1dbfb384b9324ba80b1e1ee86efde582\",\"label\":\"AccessModel Code\",\"field\":\"accessModel_code\",\"filter\":true,\"fields\":[\"accessModel_code\"],\"visible\":true},{\"id\":\"col_8_f24904e8e28164f1737337f3d1a4cd39\",\"label\":\"AccessModel Description\",\"field\":\"accessModel_description\",\"filter\":true,\"fields\":[\"accessModel_description\"],\"visible\":true},{\"id\":\"col_9_081246008528e5e66e861b4be40e46c1\",\"label\":\"ServicePlan Code\",\"field\":\"servicePlan_code\",\"filter\":true,\"fields\":[\"servicePlan_code\"],\"visible\":true},{\"id\":\"col_10_be1efbd7fa388834c65459649cb9ed83\",\"label\":\"ServicePlan Description\",\"field\":\"servicePlan_description\",\"filter\":true,\"fields\":[\"servicePlan_description\"],\"visible\":true},{\"id\":\"col_11_8e86dc40cc92baaae006690354e0c55a\",\"label\":\"Connectivity Code\",\"field\":\"connectivity_code\",\"filter\":true,\"fields\":[\"connectivity_code\"],\"visible\":true},{\"id\":\"col_12_74fcc3f307f285e1b2c87469a9fbea88\",\"label\":\"Connectivity Description\",\"field\":\"connectivity_description\",\"filter\":true,\"fields\":[\"connectivity_description\"],\"visible\":true},{\"id\":\"col_13_2c0d9b84f228287239129348a73e0500\",\"label\":\"Term Code\",\"field\":\"term_code\",\"filter\":true,\"fields\":[\"term_code\"],\"visible\":true},{\"id\":\"col_14_3aff2796e8cd499d936c75b7f602a1a3\",\"label\":\"Term Description\",\"field\":\"term_description\",\"filter\":true,\"fields\":[\"term_description\"],\"visible\":true},{\"id\":\"col_15_677e8ed7a86a4a01a69a9b2d3deb41cb\",\"label\":\"LifeCycleState\",\"field\":\"lifeCycleState\",\"filter\":true,\"fields\":[\"lifeCycleState\"],\"visible\":true},{\"id\":\"col_16_1dd60996e7ef4cc0b5eeb8082b784fcd\",\"label\":\"RenewOnlyDate\",\"field\":\"renewOnlyDate\",\"filter\":true,\"fields\":[\"renewOnlyDate\"],\"visible\":true},{\"id\":\"col_17_0e1981da9b4dab6b112791274a8fcbb1\",\"label\":\"DiscontinueDate\",\"field\":\"discontinueDate\",\"filter\":true,\"fields\":[\"discontinueDate\"],\"visible\":true},{\"id\":\"col_18_ad36ecc126d11a0822e1ea6a612ecf22\",\"label\":\"OrderAction\",\"field\":\"orderAction\",\"filter\":true,\"fields\":[\"orderAction\"],\"visible\":true},{\"id\":\"col_19_68e216ee4cadca92f87189136091dffc\",\"label\":\"SpecialProgramDiscount Code\",\"field\":\"specialProgramDiscount_code\",\"filter\":true,\"fields\":[\"specialProgramDiscount_code\"],\"visible\":true},{\"id\":\"col_20_1d28deccfcb849d8700eaa730294a80a\",\"label\":\"SpecialProgramDiscount Description\",\"field\":\"specialProgramDiscount_description\",\"filter\":true,\"fields\":[\"specialProgramDiscount_description\"],\"visible\":true},{\"id\":\"col_21_9fb35d4ab3a0d10ba25b3fbc3be26d3e\",\"label\":\"FromQty\",\"field\":\"fromQty\",\"filter\":true,\"fields\":[\"fromQty\"],\"visible\":true},{\"id\":\"col_22_6f0efddf95d66def82254e208761d507\",\"label\":\"ToQty\",\"field\":\"toQty\",\"filter\":true,\"fields\":[\"toQty\"],\"visible\":true},{\"id\":\"col_23_1af0389838508d7016a9841eb6273962\",\"label\":\"Currency\",\"field\":\"currency\",\"filter\":true,\"fields\":[\"currency\"],\"visible\":true},{\"id\":\"col_24_fdd51ddf7583a729a503d339b1e05364\",\"label\":\"SRP\",\"field\":\"SRP\",\"filter\":true,\"fields\":[\"SRP\"],\"visible\":true},{\"id\":\"col_25_1301694babf4d50d33c0105a88f3dced\",\"label\":\"CostAfterSpecialProgramDiscount\",\"field\":\"costAfterSpecialProgramDiscount\",\"filter\":true,\"fields\":[\"costAfterSpecialProgramDiscount\"],\"visible\":true},{\"id\":\"col_26_66dd7f4db6416c62606f129303a26c7a\",\"label\":\"RenewalDiscountPercent\",\"field\":\"renewalDiscountPercent\",\"filter\":true,\"fields\":[\"renewalDiscountPercent\"],\"visible\":true},{\"id\":\"col_27_d471f8556ece21ccbc8f463456a759ac\",\"label\":\"RenewalDiscountAmount\",\"field\":\"renewalDiscountAmount\",\"filter\":true,\"fields\":[\"renewalDiscountAmount\"],\"visible\":true},{\"id\":\"col_28_996918201154fceac3686e17cfdb7123\",\"label\":\"CostAfterRenewalDiscount\",\"field\":\"costAfterRenewalDiscount\",\"filter\":true,\"fields\":[\"costAfterRenewalDiscount\"],\"visible\":true},{\"id\":\"col_29_af94a3e5bac4d68174010029cd0f289c\",\"label\":\"TransactionVolumeDiscountPercent\",\"field\":\"transactionVolumeDiscountPercent\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountPercent\"],\"visible\":true},{\"id\":\"col_30_d06f6b20f2d588a97bb5c355535b8ce4\",\"label\":\"TransactionVolumeDiscountAmount\",\"field\":\"transactionVolumeDiscountAmount\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountAmount\"],\"visible\":true},{\"id\":\"col_31_1df16f13d9f5890ae574285588616a39\",\"label\":\"CostAfterTransactionVolumeDiscount\",\"field\":\"costAfterTransactionVolumeDiscount\",\"filter\":true,\"fields\":[\"costAfterTransactionVolumeDiscount\"],\"visible\":true},{\"id\":\"col_32_71622f3b0c5ad40e25a10e7ff0fd90d3\",\"label\":\"ServiceDurationDiscountPercent\",\"field\":\"serviceDurationDiscountPercent\",\"filter\":true,\"fields\":[\"serviceDurationDiscountPercent\"],\"visible\":true},{\"id\":\"col_33_815dcd28d4f671cead2da2039499cb27\",\"label\":\"ServiceDurationDiscountAmount\",\"field\":\"serviceDurationDiscountAmount\",\"filter\":true,\"fields\":[\"serviceDurationDiscountAmount\"],\"visible\":true},{\"id\":\"col_34_41c6f4fe10e151e496511d1e1233757c\",\"label\":\"CostAfterServiceDurationDiscount\",\"field\":\"costAfterServiceDurationDiscount\",\"filter\":true,\"fields\":[\"costAfterServiceDurationDiscount\"],\"visible\":true},{\"id\":\"col_35_f09c9dffb716528c44c97e86e9f5a5b0\",\"label\":\"EffectiveStartDate\",\"field\":\"effectiveStartDate\",\"filter\":true,\"fields\":[\"effectiveStartDate\"],\"visible\":true},{\"id\":\"col_36_89a1b222e53cc31b2c4909e4b9cc67b6\",\"label\":\"EffectiveEndDate\",\"field\":\"effectiveEndDate\",\"filter\":true,\"fields\":[\"effectiveEndDate\"],\"visible\":true},{\"id\":\"col_37_c33b4e2749b27eacb05cf8ec70866f17\",\"label\":\"ChangeFlag\",\"field\":\"changeFlag\",\"filter\":true,\"fields\":[\"changeFlag\"],\"visible\":true},{\"id\":\"col_38_ad78e74e6c1cf574bdeb3f261682009a\",\"label\":\"Last Change\",\"field\":\"last_change\",\"filter\":true,\"fields\":[\"last_change\"],\"visible\":true},{\"id\":\"col_39_54870b0544e3d66e1d3bc30dea6d0600\",\"label\":\"Last Modified\",\"field\":\"last_modified\",\"filter\":true,\"fields\":[\"last_modified\"],\"visible\":true},{\"id\":\"col_40_fde81f1177b1541755b672c9dc97315a\",\"label\":\"Created At\",\"field\":\"created_at\",\"filter\":true,\"fields\":[\"created_at\"],\"visible\":true}],\"created_at\":\"2025-08-18 14:20:46\",\"data_source_type\":\"data_source\",\"data_source_id\":63,\"updated_at\":\"2025-08-18 15:44:53\"}",":set_updated_at":"2025-08-18 15:44:53",":set_table_name":"autodesk_products",":set_data_source_id":63,":where_id_0":2}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 447\n         <strong>Arguments:</strong> \n         0: "UPDATE autobooks_data_table_storage SET `configuration` = :set_configuration, `updated_at` = :set_updated_at, `table_name` = :set_table_name, `data_source_id` = :set_data_source_id WHERE `id` = :where_id_0"\n         1: {":set_configuration":"{\"structure\":[{\"id\":\"col_0_82f043ee7127e7d5d23cc91ebbc5e26d\",\"label\":\"Unique Hash\",\"field\":\"unique_hash\",\"filter\":true,\"fields\":[\"unique_hash\"],\"visible\":true},{\"id\":\"col_1_a62d3a3e711ed6ac93df1562f96a952b\",\"label\":\"Hash String\",\"field\":\"hash_string\",\"filter\":true,\"fields\":[\"hash_string\"],\"visible\":true},{\"id\":\"col_2_4c5668167885bf8500439825c10c1cad\",\"label\":\"OfferingName\",\"field\":\"offeringName\",\"filter\":true,\"fields\":[\"offeringName\"],\"visible\":true},{\"id\":\"col_3_999609054aa657925a5d027e40ba4798\",\"label\":\"OfferingCode\",\"field\":\"offeringCode\",\"filter\":true,\"fields\":[\"offeringCode\"],\"visible\":true},{\"id\":\"col_4_705b8e0aea7f809b7b64c8b982cd888c\",\"label\":\"OfferingId\",\"field\":\"offeringId\",\"filter\":true,\"fields\":[\"offeringId\"],\"visible\":true},{\"id\":\"col_5_f5ea461ed2b96a9258aea9b862a65d97\",\"label\":\"IntendedUsage Code\",\"field\":\"intendedUsage_code\",\"filter\":true,\"fields\":[\"intendedUsage_code\"],\"visible\":true},{\"id\":\"col_6_fa3c14c0ab91af3cf8681adc118caa5e\",\"label\":\"IntendedUsage Description\",\"field\":\"intendedUsage_description\",\"filter\":true,\"fields\":[\"intendedUsage_description\"],\"visible\":true},{\"id\":\"col_7_1dbfb384b9324ba80b1e1ee86efde582\",\"label\":\"AccessModel Code\",\"field\":\"accessModel_code\",\"filter\":true,\"fields\":[\"accessModel_code\"],\"visible\":true},{\"id\":\"col_8_f24904e8e28164f1737337f3d1a4cd39\",\"label\":\"AccessModel Description\",\"field\":\"accessModel_description\",\"filter\":true,\"fields\":[\"accessModel_description\"],\"visible\":true},{\"id\":\"col_9_081246008528e5e66e861b4be40e46c1\",\"label\":\"ServicePlan Code\",\"field\":\"servicePlan_code\",\"filter\":true,\"fields\":[\"servicePlan_code\"],\"visible\":true},{\"id\":\"col_10_be1efbd7fa388834c65459649cb9ed83\",\"label\":\"ServicePlan Description\",\"field\":\"servicePlan_description\",\"filter\":true,\"fields\":[\"servicePlan_description\"],\"visible\":true},{\"id\":\"col_11_8e86dc40cc92baaae006690354e0c55a\",\"label\":\"Connectivity Code\",\"field\":\"connectivity_code\",\"filter\":true,\"fields\":[\"connectivity_code\"],\"visible\":true},{\"id\":\"col_12_74fcc3f307f285e1b2c87469a9fbea88\",\"label\":\"Connectivity Description\",\"field\":\"connectivity_description\",\"filter\":true,\"fields\":[\"connectivity_description\"],\"visible\":true},{\"id\":\"col_13_2c0d9b84f228287239129348a73e0500\",\"label\":\"Term Code\",\"field\":\"term_code\",\"filter\":true,\"fields\":[\"term_code\"],\"visible\":true},{\"id\":\"col_14_3aff2796e8cd499d936c75b7f602a1a3\",\"label\":\"Term Description\",\"field\":\"term_description\",\"filter\":true,\"fields\":[\"term_description\"],\"visible\":true},{\"id\":\"col_15_677e8ed7a86a4a01a69a9b2d3deb41cb\",\"label\":\"LifeCycleState\",\"field\":\"lifeCycleState\",\"filter\":true,\"fields\":[\"lifeCycleState\"],\"visible\":true},{\"id\":\"col_16_1dd60996e7ef4cc0b5eeb8082b784fcd\",\"label\":\"RenewOnlyDate\",\"field\":\"renewOnlyDate\",\"filter\":true,\"fields\":[\"renewOnlyDate\"],\"visible\":true},{\"id\":\"col_17_0e1981da9b4dab6b112791274a8fcbb1\",\"label\":\"DiscontinueDate\",\"field\":\"discontinueDate\",\"filter\":true,\"fields\":[\"discontinueDate\"],\"visible\":true},{\"id\":\"col_18_ad36ecc126d11a0822e1ea6a612ecf22\",\"label\":\"OrderAction\",\"field\":\"orderAction\",\"filter\":true,\"fields\":[\"orderAction\"],\"visible\":true},{\"id\":\"col_19_68e216ee4cadca92f87189136091dffc\",\"label\":\"SpecialProgramDiscount Code\",\"field\":\"specialProgramDiscount_code\",\"filter\":true,\"fields\":[\"specialProgramDiscount_code\"],\"visible\":true},{\"id\":\"col_20_1d28deccfcb849d8700eaa730294a80a\",\"label\":\"SpecialProgramDiscount Description\",\"field\":\"specialProgramDiscount_description\",\"filter\":true,\"fields\":[\"specialProgramDiscount_description\"],\"visible\":true},{\"id\":\"col_21_9fb35d4ab3a0d10ba25b3fbc3be26d3e\",\"label\":\"FromQty\",\"field\":\"fromQty\",\"filter\":true,\"fields\":[\"fromQty\"],\"visible\":true},{\"id\":\"col_22_6f0efddf95d66def82254e208761d507\",\"label\":\"ToQty\",\"field\":\"toQty\",\"filter\":true,\"fields\":[\"toQty\"],\"visible\":true},{\"id\":\"col_23_1af0389838508d7016a9841eb6273962\",\"label\":\"Currency\",\"field\":\"currency\",\"filter\":true,\"fields\":[\"currency\"],\"visible\":true},{\"id\":\"col_24_fdd51ddf7583a729a503d339b1e05364\",\"label\":\"SRP\",\"field\":\"SRP\",\"filter\":true,\"fields\":[\"SRP\"],\"visible\":true},{\"id\":\"col_25_1301694babf4d50d33c0105a88f3dced\",\"label\":\"CostAfterSpecialProgramDiscount\",\"field\":\"costAfterSpecialProgramDiscount\",\"filter\":true,\"fields\":[\"costAfterSpecialProgramDiscount\"],\"visible\":true},{\"id\":\"col_26_66dd7f4db6416c62606f129303a26c7a\",\"label\":\"RenewalDiscountPercent\",\"field\":\"renewalDiscountPercent\",\"filter\":true,\"fields\":[\"renewalDiscountPercent\"],\"visible\":true},{\"id\":\"col_27_d471f8556ece21ccbc8f463456a759ac\",\"label\":\"RenewalDiscountAmount\",\"field\":\"renewalDiscountAmount\",\"filter\":true,\"fields\":[\"renewalDiscountAmount\"],\"visible\":true},{\"id\":\"col_28_996918201154fceac3686e17cfdb7123\",\"label\":\"CostAfterRenewalDiscount\",\"field\":\"costAfterRenewalDiscount\",\"filter\":true,\"fields\":[\"costAfterRenewalDiscount\"],\"visible\":true},{\"id\":\"col_29_af94a3e5bac4d68174010029cd0f289c\",\"label\":\"TransactionVolumeDiscountPercent\",\"field\":\"transactionVolumeDiscountPercent\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountPercent\"],\"visible\":true},{\"id\":\"col_30_d06f6b20f2d588a97bb5c355535b8ce4\",\"label\":\"TransactionVolumeDiscountAmount\",\"field\":\"transactionVolumeDiscountAmount\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountAmount\"],\"visible\":true},{\"id\":\"col_31_1df16f13d9f5890ae574285588616a39\",\"label\":\"CostAfterTransactionVolumeDiscount\",\"field\":\"costAfterTransactionVolumeDiscount\",\"filter\":true,\"fields\":[\"costAfterTransactionVolumeDiscount\"],\"visible\":true},{\"id\":\"col_32_71622f3b0c5ad40e25a10e7ff0fd90d3\",\"label\":\"ServiceDurationDiscountPercent\",\"field\":\"serviceDurationDiscountPercent\",\"filter\":true,\"fields\":[\"serviceDurationDiscountPercent\"],\"visible\":true},{\"id\":\"col_33_815dcd28d4f671cead2da2039499cb27\",\"label\":\"ServiceDurationDiscountAmount\",\"field\":\"serviceDurationDiscountAmount\",\"filter\":true,\"fields\":[\"serviceDurationDiscountAmount\"],\"visible\":true},{\"id\":\"col_34_41c6f4fe10e151e496511d1e1233757c\",\"label\":\"CostAfterServiceDurationDiscount\",\"field\":\"costAfterServiceDurationDiscount\",\"filter\":true,\"fields\":[\"costAfterServiceDurationDiscount\"],\"visible\":true},{\"id\":\"col_35_f09c9dffb716528c44c97e86e9f5a5b0\",\"label\":\"EffectiveStartDate\",\"field\":\"effectiveStartDate\",\"filter\":true,\"fields\":[\"effectiveStartDate\"],\"visible\":true},{\"id\":\"col_36_89a1b222e53cc31b2c4909e4b9cc67b6\",\"label\":\"EffectiveEndDate\",\"field\":\"effectiveEndDate\",\"filter\":true,\"fields\":[\"effectiveEndDate\"],\"visible\":true},{\"id\":\"col_37_c33b4e2749b27eacb05cf8ec70866f17\",\"label\":\"ChangeFlag\",\"field\":\"changeFlag\",\"filter\":true,\"fields\":[\"changeFlag\"],\"visible\":true},{\"id\":\"col_38_ad78e74e6c1cf574bdeb3f261682009a\",\"label\":\"Last Change\",\"field\":\"last_change\",\"filter\":true,\"fields\":[\"last_change\"],\"visible\":true},{\"id\":\"col_39_54870b0544e3d66e1d3bc30dea6d0600\",\"label\":\"Last Modified\",\"field\":\"last_modified\",\"filter\":true,\"fields\":[\"last_modified\"],\"visible\":true},{\"id\":\"col_40_fde81f1177b1541755b672c9dc97315a\",\"label\":\"Created At\",\"field\":\"created_at\",\"filter\":true,\"fields\":[\"created_at\"],\"visible\":true}],\"hidden\":[],\"columns\":[{\"id\":\"col_0_82f043ee7127e7d5d23cc91ebbc5e26d\",\"label\":\"Unique Hash\",\"field\":\"unique_hash\",\"filter\":true,\"fields\":[\"unique_hash\"],\"visible\":true},{\"id\":\"col_1_a62d3a3e711ed6ac93df1562f96a952b\",\"label\":\"Hash String\",\"field\":\"hash_string\",\"filter\":true,\"fields\":[\"hash_string\"],\"visible\":true},{\"id\":\"col_2_4c5668167885bf8500439825c10c1cad\",\"label\":\"OfferingName\",\"field\":\"offeringName\",\"filter\":true,\"fields\":[\"offeringName\"],\"visible\":true},{\"id\":\"col_3_999609054aa657925a5d027e40ba4798\",\"label\":\"OfferingCode\",\"field\":\"offeringCode\",\"filter\":true,\"fields\":[\"offeringCode\"],\"visible\":true},{\"id\":\"col_4_705b8e0aea7f809b7b64c8b982cd888c\",\"label\":\"OfferingId\",\"field\":\"offeringId\",\"filter\":true,\"fields\":[\"offeringId\"],\"visible\":true},{\"id\":\"col_5_f5ea461ed2b96a9258aea9b862a65d97\",\"label\":\"IntendedUsage Code\",\"field\":\"intendedUsage_code\",\"filter\":true,\"fields\":[\"intendedUsage_code\"],\"visible\":true},{\"id\":\"col_6_fa3c14c0ab91af3cf8681adc118caa5e\",\"label\":\"IntendedUsage Description\",\"field\":\"intendedUsage_description\",\"filter\":true,\"fields\":[\"intendedUsage_description\"],\"visible\":true},{\"id\":\"col_7_1dbfb384b9324ba80b1e1ee86efde582\",\"label\":\"AccessModel Code\",\"field\":\"accessModel_code\",\"filter\":true,\"fields\":[\"accessModel_code\"],\"visible\":true},{\"id\":\"col_8_f24904e8e28164f1737337f3d1a4cd39\",\"label\":\"AccessModel Description\",\"field\":\"accessModel_description\",\"filter\":true,\"fields\":[\"accessModel_description\"],\"visible\":true},{\"id\":\"col_9_081246008528e5e66e861b4be40e46c1\",\"label\":\"ServicePlan Code\",\"field\":\"servicePlan_code\",\"filter\":true,\"fields\":[\"servicePlan_code\"],\"visible\":true},{\"id\":\"col_10_be1efbd7fa388834c65459649cb9ed83\",\"label\":\"ServicePlan Description\",\"field\":\"servicePlan_description\",\"filter\":true,\"fields\":[\"servicePlan_description\"],\"visible\":true},{\"id\":\"col_11_8e86dc40cc92baaae006690354e0c55a\",\"label\":\"Connectivity Code\",\"field\":\"connectivity_code\",\"filter\":true,\"fields\":[\"connectivity_code\"],\"visible\":true},{\"id\":\"col_12_74fcc3f307f285e1b2c87469a9fbea88\",\"label\":\"Connectivity Description\",\"field\":\"connectivity_description\",\"filter\":true,\"fields\":[\"connectivity_description\"],\"visible\":true},{\"id\":\"col_13_2c0d9b84f228287239129348a73e0500\",\"label\":\"Term Code\",\"field\":\"term_code\",\"filter\":true,\"fields\":[\"term_code\"],\"visible\":true},{\"id\":\"col_14_3aff2796e8cd499d936c75b7f602a1a3\",\"label\":\"Term Description\",\"field\":\"term_description\",\"filter\":true,\"fields\":[\"term_description\"],\"visible\":true},{\"id\":\"col_15_677e8ed7a86a4a01a69a9b2d3deb41cb\",\"label\":\"LifeCycleState\",\"field\":\"lifeCycleState\",\"filter\":true,\"fields\":[\"lifeCycleState\"],\"visible\":true},{\"id\":\"col_16_1dd60996e7ef4cc0b5eeb8082b784fcd\",\"label\":\"RenewOnlyDate\",\"field\":\"renewOnlyDate\",\"filter\":true,\"fields\":[\"renewOnlyDate\"],\"visible\":true},{\"id\":\"col_17_0e1981da9b4dab6b112791274a8fcbb1\",\"label\":\"DiscontinueDate\",\"field\":\"discontinueDate\",\"filter\":true,\"fields\":[\"discontinueDate\"],\"visible\":true},{\"id\":\"col_18_ad36ecc126d11a0822e1ea6a612ecf22\",\"label\":\"OrderAction\",\"field\":\"orderAction\",\"filter\":true,\"fields\":[\"orderAction\"],\"visible\":true},{\"id\":\"col_19_68e216ee4cadca92f87189136091dffc\",\"label\":\"SpecialProgramDiscount Code\",\"field\":\"specialProgramDiscount_code\",\"filter\":true,\"fields\":[\"specialProgramDiscount_code\"],\"visible\":true},{\"id\":\"col_20_1d28deccfcb849d8700eaa730294a80a\",\"label\":\"SpecialProgramDiscount Description\",\"field\":\"specialProgramDiscount_description\",\"filter\":true,\"fields\":[\"specialProgramDiscount_description\"],\"visible\":true},{\"id\":\"col_21_9fb35d4ab3a0d10ba25b3fbc3be26d3e\",\"label\":\"FromQty\",\"field\":\"fromQty\",\"filter\":true,\"fields\":[\"fromQty\"],\"visible\":true},{\"id\":\"col_22_6f0efddf95d66def82254e208761d507\",\"label\":\"ToQty\",\"field\":\"toQty\",\"filter\":true,\"fields\":[\"toQty\"],\"visible\":true},{\"id\":\"col_23_1af0389838508d7016a9841eb6273962\",\"label\":\"Currency\",\"field\":\"currency\",\"filter\":true,\"fields\":[\"currency\"],\"visible\":true},{\"id\":\"col_24_fdd51ddf7583a729a503d339b1e05364\",\"label\":\"SRP\",\"field\":\"SRP\",\"filter\":true,\"fields\":[\"SRP\"],\"visible\":true},{\"id\":\"col_25_1301694babf4d50d33c0105a88f3dced\",\"label\":\"CostAfterSpecialProgramDiscount\",\"field\":\"costAfterSpecialProgramDiscount\",\"filter\":true,\"fields\":[\"costAfterSpecialProgramDiscount\"],\"visible\":true},{\"id\":\"col_26_66dd7f4db6416c62606f129303a26c7a\",\"label\":\"RenewalDiscountPercent\",\"field\":\"renewalDiscountPercent\",\"filter\":true,\"fields\":[\"renewalDiscountPercent\"],\"visible\":true},{\"id\":\"col_27_d471f8556ece21ccbc8f463456a759ac\",\"label\":\"RenewalDiscountAmount\",\"field\":\"renewalDiscountAmount\",\"filter\":true,\"fields\":[\"renewalDiscountAmount\"],\"visible\":true},{\"id\":\"col_28_996918201154fceac3686e17cfdb7123\",\"label\":\"CostAfterRenewalDiscount\",\"field\":\"costAfterRenewalDiscount\",\"filter\":true,\"fields\":[\"costAfterRenewalDiscount\"],\"visible\":true},{\"id\":\"col_29_af94a3e5bac4d68174010029cd0f289c\",\"label\":\"TransactionVolumeDiscountPercent\",\"field\":\"transactionVolumeDiscountPercent\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountPercent\"],\"visible\":true},{\"id\":\"col_30_d06f6b20f2d588a97bb5c355535b8ce4\",\"label\":\"TransactionVolumeDiscountAmount\",\"field\":\"transactionVolumeDiscountAmount\",\"filter\":true,\"fields\":[\"transactionVolumeDiscountAmount\"],\"visible\":true},{\"id\":\"col_31_1df16f13d9f5890ae574285588616a39\",\"label\":\"CostAfterTransactionVolumeDiscount\",\"field\":\"costAfterTransactionVolumeDiscount\",\"filter\":true,\"fields\":[\"costAfterTransactionVolumeDiscount\"],\"visible\":true},{\"id\":\"col_32_71622f3b0c5ad40e25a10e7ff0fd90d3\",\"label\":\"ServiceDurationDiscountPercent\",\"field\":\"serviceDurationDiscountPercent\",\"filter\":true,\"fields\":[\"serviceDurationDiscountPercent\"],\"visible\":true},{\"id\":\"col_33_815dcd28d4f671cead2da2039499cb27\",\"label\":\"ServiceDurationDiscountAmount\",\"field\":\"serviceDurationDiscountAmount\",\"filter\":true,\"fields\":[\"serviceDurationDiscountAmount\"],\"visible\":true},{\"id\":\"col_34_41c6f4fe10e151e496511d1e1233757c\",\"label\":\"CostAfterServiceDurationDiscount\",\"field\":\"costAfterServiceDurationDiscount\",\"filter\":true,\"fields\":[\"costAfterServiceDurationDiscount\"],\"visible\":true},{\"id\":\"col_35_f09c9dffb716528c44c97e86e9f5a5b0\",\"label\":\"EffectiveStartDate\",\"field\":\"effectiveStartDate\",\"filter\":true,\"fields\":[\"effectiveStartDate\"],\"visible\":true},{\"id\":\"col_36_89a1b222e53cc31b2c4909e4b9cc67b6\",\"label\":\"EffectiveEndDate\",\"field\":\"effectiveEndDate\",\"filter\":true,\"fields\":[\"effectiveEndDate\"],\"visible\":true},{\"id\":\"col_37_c33b4e2749b27eacb05cf8ec70866f17\",\"label\":\"ChangeFlag\",\"field\":\"changeFlag\",\"filter\":true,\"fields\":[\"changeFlag\"],\"visible\":true},{\"id\":\"col_38_ad78e74e6c1cf574bdeb3f261682009a\",\"label\":\"Last Change\",\"field\":\"last_change\",\"filter\":true,\"fields\":[\"last_change\"],\"visible\":true},{\"id\":\"col_39_54870b0544e3d66e1d3bc30dea6d0600\",\"label\":\"Last Modified\",\"field\":\"last_modified\",\"filter\":true,\"fields\":[\"last_modified\"],\"visible\":true},{\"id\":\"col_40_fde81f1177b1541755b672c9dc97315a\",\"label\":\"Created At\",\"field\":\"created_at\",\"filter\":true,\"fields\":[\"created_at\"],\"visible\":true}],\"created_at\":\"2025-08-18 14:20:46\",\"data_source_type\":\"data_source\",\"data_source_id\":63,\"updated_at\":\"2025-08-18 15:44:53\"}",":set_updated_at":"2025-08-18 15:44:53",":set_table_name":"autodesk_products",":set_data_source_id":63,":where_id_0":2}\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(223) "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `data_source_id` = :where_data_source_id_1 AND `user_id` = :where_user_id_2 LIMIT 1"\n  ["params"]: array(3) {\n    [":where_table_name_0"]: string(17) "autodesk_products"\n    [":where_data_source_id_1"]: int(63)\n    [":where_user_id_2"]: int(2)\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `data_source_id` = :where_data_source_id_1 AND `user_id` = :where_user_id_2 LIMIT 1","params":{":where_table_name_0":"autodesk_products",":where_data_source_id_1":63,":where_user_id_2":2},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `data_source_id` = :where_data_source_id_1 AND `user_id` = :where_user_id_2 LIMIT 1"\n         1: {":where_table_name_0":"autodesk_products",":where_data_source_id_1":63,":where_user_id_2":2}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `data_source_id` = :where_data_source_id_1 AND `user_id` = :where_user_id_2 LIMIT 1"\n         1: {":where_table_name_0":"autodesk_products",":where_data_source_id_1":63,":where_user_id_2":2}\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(120) "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n  ["params"]: array(0) {\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'","params":[],"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n         1: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n         1: []\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(124) "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n  ["params"]: array(0) {\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'","params":[],"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n         1: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'"\n         1: []\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(69) "SELECT * FROM autobooks_data_sources WHERE `id` = :where_id_0 LIMIT 1"\n  ["params"]: array(1) {\n    [":where_id_0"]: int(63)\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(22) "autobooks_data_sources"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT * FROM autobooks_data_sources WHERE `id` = :where_id_0 LIMIT 1","params":{":where_id_0":63},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_sources"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_sources WHERE `id` = :where_id_0 LIMIT 1"\n         1: {":where_id_0":63}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_sources WHERE `id` = :where_id_0 LIMIT 1"\n         1: {":where_id_0":63}\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(41) "SELECT * FROM `products_autodesk_catalog`"\n  ["params"]: array(0) {\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(22) "autobooks_data_sources"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT * FROM `products_autodesk_catalog`","params":[],"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_sources"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM `products_autodesk_catalog`"\n         1: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM `products_autodesk_catalog`"\n         1: []\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(223) "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `data_source_id` = :where_data_source_id_1 AND `user_id` = :where_user_id_2 LIMIT 1"\n  ["params"]: array(3) {\n    [":where_table_name_0"]: string(17) "autodesk_products"\n    [":where_data_source_id_1"]: int(63)\n    [":where_user_id_2"]: int(2)\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `data_source_id` = :where_data_source_id_1 AND `user_id` = :where_user_id_2 LIMIT 1","params":{":where_table_name_0":"autodesk_products",":where_data_source_id_1":63,":where_user_id_2":2},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `data_source_id` = :where_data_source_id_1 AND `user_id` = :where_user_id_2 LIMIT 1"\n         1: {":where_table_name_0":"autodesk_products",":where_data_source_id_1":63,":where_user_id_2":2}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `data_source_id` = :where_data_source_id_1 AND `user_id` = :where_user_id_2 LIMIT 1"\n         1: {":where_table_name_0":"autodesk_products",":where_data_source_id_1":63,":where_user_id_2":2}\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(69) "SELECT * FROM autobooks_data_sources WHERE `status` = :where_status_0"\n  ["params"]: array(1) {\n    [":where_status_0"]: string(6) "active"\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(22) "autobooks_data_sources"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT * FROM autobooks_data_sources WHERE `status` = :where_status_0","params":{":where_status_0":"active"},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_sources"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_sources WHERE `status` = :where_status_0"\n         1: {":where_status_0":"active"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_sources WHERE `status` = :where_status_0"\n         1: {":where_status_0":"active"}\n-->\n
[database_queries] [2025-08-18 15:44:53] [database.class.php:851] \n<!--array(4) {\n  ["query"]: string(176) "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `user_id` = :where_user_id_1 LIMIT 1"\n  ["params"]: array(2) {\n    [":where_table_name_0"]: string(17) "autodesk_products"\n    [":where_user_id_1"]: int(2)\n  }\n  ["timestamp"]: string(19) "2025-08-18 15:44:53"\n  ["table"]: string(28) "autobooks_data_table_storage"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 851\n         <strong>Arguments:</strong> \n         0: {"query":"SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `user_id` = :where_user_id_1 LIMIT 1","params":{":where_table_name_0":"autodesk_products",":where_user_id_1":2},"timestamp":"2025-08-18 15:44:53","table":"autobooks_data_table_storage"}\n         1: "database_queries"\n         2: true\n      <strong>Function:</strong> logQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 681\n         <strong>Arguments:</strong> \n         0: "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `user_id` = :where_user_id_1 LIMIT 1"\n         1: {":where_table_name_0":"autodesk_products",":where_user_id_1":2}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT configuration, data_source_id, updated_at, table_name FROM autobooks_data_table_storage WHERE `table_name` = :where_table_name_0 AND `user_id` = :where_user_id_1 LIMIT 1"\n         1: {":where_table_name_0":"autodesk_products",":where_user_id_1":2}\n-->\n
