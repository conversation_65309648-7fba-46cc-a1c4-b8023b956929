[database_errors] [2025-08-14 14:18:10] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(82) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'file_path' in 'field list'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(42) "Unknown column 'file_path' in 'field list'"\n  ["query"]: string(175) "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(27) "autobooks_navigation as nav"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(55) "/baffletrain/autocadlt/autobooks/data_sources/edit_view"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(675) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(260): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/paths.php(283): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/startup_sequence.class.php(19): build_routes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): startup_sequence::start()\n#5 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'file_path' in 'field list'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'file_path' in 'field list'","query":"SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC","parameters":[],"table":"autobooks_navigation as nav","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/data_sources\/edit_view","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(260): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/paths.php(283): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/startup_sequence.class.php(19): build_routes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): startup_sequence::start()\n#5 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'file_path' in 'field list'"]}\n         1: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n         1: []\n-->\n
[database_errors] [2025-08-14 15:07:34] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(113) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_navigation' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(61) "Table 'wwwcadservicescouk.autobooks_navigation' doesn't exist"\n  ["query"]: string(175) "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(27) "autobooks_navigation as nav"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(55) "/baffletrain/autocadlt/autobooks/data_sources/edit_view"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(675) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(260): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/paths.php(283): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/startup_sequence.class.php(19): build_routes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): startup_sequence::start()\n#5 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_navigation' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_navigation' doesn't exist","query":"SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC","parameters":[],"table":"autobooks_navigation as nav","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/data_sources\/edit_view","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(260): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/paths.php(283): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/startup_sequence.class.php(19): build_routes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): startup_sequence::start()\n#5 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_navigation' doesn't exist"]}\n         1: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n         1: []\n-->\n
[database_errors] [2025-08-14 20:15:52] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(946): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(980): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(946): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(980): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 946\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 20:28:14] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:13:56] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:14:13] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:14:18] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:30:01] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:31:46] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:46:12] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:46:58] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:49:33] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:51:48] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:52:17] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(944): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(978): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(944): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(978): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 944\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:54:02] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:55:34] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(955): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(989): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(955): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(989): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 955\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:56:11] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(955): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(989): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(955): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(989): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 955\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:56:34] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(955): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(989): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(955): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(989): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 955\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 21:56:41] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(955): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(989): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(955): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(989): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 955\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 22:07:56] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(955): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(989): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(955): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(989): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 955\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 22:09:40] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 22:23:20] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 22:25:57] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(75): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(75): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 22:40:42] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 22:56:08] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 23:06:55] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(18) "SHOW TABLES LIKE ?"\n  ["parameters"]: array(1) {\n    [0]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE ?","parameters":["autobooks_%"],"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE ?"\n         2: ["autobooks_%"]\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE ?"\n         1: ["autobooks_%"]\n-->\n
[database_errors] [2025-08-14 23:30:12] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(25) "SHOW TABLES LIKE :pattern"\n  ["parameters"]: array(1) {\n    [":pattern"]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE :pattern","parameters":{":pattern":"autobooks_%"},"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE :pattern"\n         2: {":pattern":"autobooks_%"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE :pattern"\n         1: {":pattern":"autobooks_%"}\n-->\n
[database_errors] [2025-08-14 23:31:54] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(25) "SHOW TABLES LIKE :pattern"\n  ["parameters"]: array(1) {\n    [":pattern"]: string(13) "'autobooks_%'"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE :pattern","parameters":{":pattern":"'autobooks_%'"},"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE :pattern"\n         2: {":pattern":"'autobooks_%'"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE :pattern"\n         1: {":pattern":"'autobooks_%'"}\n-->\n
[database_errors] [2025-08-14 23:31:54] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(25) "SHOW TABLES LIKE :pattern"\n  ["parameters"]: array(1) {\n    [":pattern"]: string(13) "'autobooks_%'"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE :pattern","parameters":{":pattern":"'autobooks_%'"},"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE :pattern"\n         2: {":pattern":"'autobooks_%'"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE :pattern"\n         1: {":pattern":"'autobooks_%'"}\n-->\n
[database_errors] [2025-08-14 23:35:02] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(205) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(149) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"\n  ["query"]: string(25) "SHOW TABLES LIKE :pattern"\n  ["parameters"]: array(1) {\n    [":pattern"]: string(11) "autobooks_%"\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(58) "/baffletrain/autocadlt/autobooks/api/dump_autobooks_tables"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1255) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(988): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views/system/database_dump.api.php(49): system\database::getTablesLike()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\system\database_dump\dump_autobooks_tables()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1","query":"SHOW TABLES LIKE :pattern","parameters":{":pattern":"autobooks_%"},"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/dump_autobooks_tables","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(988): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/views\/system\/database_dump.api.php(49): system\\database::getTablesLike()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\system\\database_dump\\dump_autobooks_tables()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"]}\n         1: "SHOW TABLES LIKE :pattern"\n         2: {":pattern":"autobooks_%"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "SHOW TABLES LIKE :pattern"\n         1: {":pattern":"autobooks_%"}\n-->\n
[database_errors] [2025-08-15 10:12:48] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(123) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(71) "Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"\n  ["query"]: string(41) "DESCRIBE `autobooks_import_bluebeam_data`"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(22) "autobooks_data_sources"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(65) "/baffletrain/autocadlt/autobooks/api/data_sources/edit_view?id=49"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1648) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(683): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_source_manager.class.php(77): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-source-builder.edge.php(329): system\data_source_manager::get_table_info()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php(979): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\data_sources\edit_view()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#12 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist","query":"DESCRIBE `autobooks_import_bluebeam_data`","parameters":[],"table":"autobooks_data_sources","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_sources\/edit_view?id=49","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(683): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_source_manager.class.php(77): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-source-builder.edge.php(329): system\\data_source_manager::get_table_info()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_sources.api.php(979): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\data_sources\\edit_view()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#12 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"]}\n         1: "DESCRIBE `autobooks_import_bluebeam_data`"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "DESCRIBE `autobooks_import_bluebeam_data`"\n         1: []\n-->\n
[database_errors] [2025-08-15 10:14:36] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(123) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(71) "Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"\n  ["query"]: string(41) "DESCRIBE `autobooks_import_bluebeam_data`"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(22) "autobooks_data_sources"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(65) "/baffletrain/autocadlt/autobooks/api/data_sources/edit_view?id=49"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1648) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(683): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_source_manager.class.php(77): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-source-builder.edge.php(329): system\data_source_manager::get_table_info()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php(979): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\data_sources\edit_view()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#12 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist","query":"DESCRIBE `autobooks_import_bluebeam_data`","parameters":[],"table":"autobooks_data_sources","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_sources\/edit_view?id=49","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(683): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_source_manager.class.php(77): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-source-builder.edge.php(329): system\\data_source_manager::get_table_info()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_sources.api.php(979): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\data_sources\\edit_view()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#12 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"]}\n         1: "DESCRIBE `autobooks_import_bluebeam_data`"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "DESCRIBE `autobooks_import_bluebeam_data`"\n         1: []\n-->\n
[database_errors] [2025-08-15 10:19:05] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(123) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(71) "Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"\n  ["query"]: string(41) "DESCRIBE `autobooks_import_bluebeam_data`"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(22) "autobooks_data_sources"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(65) "/baffletrain/autocadlt/autobooks/api/data_sources/edit_view?id=49"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1648) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(683): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(954): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_source_manager.class.php(77): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/data-source-builder.edge.php(329): system\data_source_manager::get_table_info()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php(979): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(84): api\data_sources\edit_view()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#12 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist","query":"DESCRIBE `autobooks_import_bluebeam_data`","parameters":[],"table":"autobooks_data_sources","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_sources\/edit_view?id=49","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(683): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(954): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_source_manager.class.php(77): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-source-builder.edge.php(329): system\\data_source_manager::get_table_info()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_sources.api.php(979): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(84): api\\data_sources\\edit_view()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#12 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_import_bluebeam_data' doesn't exist"]}\n         1: "DESCRIBE `autobooks_import_bluebeam_data`"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 954\n         <strong>Arguments:</strong> \n         0: "DESCRIBE `autobooks_import_bluebeam_data`"\n         1: []\n-->\n
[database_errors] [2025-08-17 19:57:24] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(91) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '0' for key 'PRIMARY'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(37) "Duplicate entry '0' for key 'PRIMARY'"\n  ["query"]: string(264) "INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)"\n  ["parameters"]: array(9) {\n    [":parent_path"]: string(4) "root"\n    [":route_key"]: string(8) "bluebeam"\n    [":name"]: string(8) "Bluebeam"\n    [":icon"]: string(8) "bluebeam"\n    [":required_roles"]: string(2) "[]"\n    [":file_path"]: string(0) ""\n    [":is_system"]: bool(false)\n    [":can_delete"]: bool(true)\n    [":sort_order"]: int(101)\n  }\n  ["table"]: string(20) "autobooks_navigation"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(69) "/baffletrain/autocadlt/autobooks/api/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1239) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(683): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(425): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(410): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(160): api\nav_tree\process_nav_entry_async()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(86): api\nav_tree\save_nav_entry_progress()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '0' for key 'PRIMARY'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry '0' for key 'PRIMARY'","query":"INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)","parameters":{":parent_path":"root",":route_key":"bluebeam",":name":"Bluebeam",":icon":"bluebeam",":required_roles":"[]",":file_path":"",":is_system":false,":can_delete":true,":sort_order":101},"table":"autobooks_navigation","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(683): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(425): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(410): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(160): api\\nav_tree\\process_nav_entry_async()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(86): api\\nav_tree\\save_nav_entry_progress()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry '0' for key 'PRIMARY'"]}\n         1: "INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)"\n         2: {":parent_path":"root",":route_key":"bluebeam",":name":"Bluebeam",":icon":"bluebeam",":required_roles":"[]",":file_path":"",":is_system":false,":can_delete":true,":sort_order":101}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 425\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)"\n         1: {":parent_path":"root",":route_key":"bluebeam",":name":"Bluebeam",":icon":"bluebeam",":required_roles":"[]",":file_path":"",":is_system":false,":can_delete":true,":sort_order":101}\n-->\n
[database_errors] [2025-08-17 20:42:12] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(85) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sold_to_name' in 'field list'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(45) "Unknown column 'sold_to_name' in 'field list'"\n  ["query"]: string(63) "SELECT sold_to_name FROM autobooks_import_bluebeam_data LIMIT 6"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(30) "autobooks_import_bluebeam_data"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(69) "/baffletrain/autocadlt/autobooks/api/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(3034) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(260): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/column_analyzer.class.php(267): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/column_analyzer.class.php(142): system\column_analyzer::get_sample_data()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/column_analyzer.class.php(57): system\column_analyzer::analyze_data_patterns()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1209): system\column_analyzer::analyze_column()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1165): system\data_importer::get_intelligent_column_name()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1442): system\data_importer::generate_enhanced_schema()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1398): system\data_importer::import_csv_with_auto_schema()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(152): system\data_importer::import_csv_to_hilt_table()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(592): system\hilt::process_template()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(309): api\nav_tree\generate_view_content()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(160): api\nav_tree\process_nav_entry_async()\n#15 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(86): api\nav_tree\save_nav_entry_progress()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#18 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#19 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#20 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sold_to_name' in 'field list'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'sold_to_name' in 'field list'","query":"SELECT sold_to_name FROM autobooks_import_bluebeam_data LIMIT 6","parameters":[],"table":"autobooks_import_bluebeam_data","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(260): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/column_analyzer.class.php(267): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/column_analyzer.class.php(142): system\\column_analyzer::get_sample_data()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/column_analyzer.class.php(57): system\\column_analyzer::analyze_data_patterns()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1209): system\\column_analyzer::analyze_column()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1165): system\\data_importer::get_intelligent_column_name()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1442): system\\data_importer::generate_enhanced_schema()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1398): system\\data_importer::import_csv_with_auto_schema()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(152): system\\data_importer::import_csv_to_hilt_table()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(592): system\\hilt::process_template()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(309): api\\nav_tree\\generate_view_content()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(160): api\\nav_tree\\process_nav_entry_async()\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(86): api\\nav_tree\\save_nav_entry_progress()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#18 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#19 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#20 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'sold_to_name' in 'field list'"]}\n         1: "SELECT sold_to_name FROM autobooks_import_bluebeam_data LIMIT 6"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT sold_to_name FROM autobooks_import_bluebeam_data LIMIT 6"\n         1: []\n-->\n
[database_errors] [2025-08-18 10:27:35] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(101) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '2' for key 'unique_table_user'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(47) "Duplicate entry '2' for key 'unique_table_user'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(0) ""\n    [":user_id"]: int(2)\n    [":configuration"]: string(7456) "{"data_source_type":"data_source","data_source_id":1,"updated_at":"2025-08-18 10:27:35","structure":[{"id":"col_0_c68c54809e9acf125fe784d260a50197","label":"Id","field":"autodesk_subscriptions_id","filter":true,"fields":["autodesk_subscriptions_id"],"visible":true},{"id":"col_1_8b965a912aecb63854dab8c6266ce829","label":"SubscriptionId","field":"autodesk_subscriptions_subscriptionId","filter":true,"fields":["autodesk_subscriptions_subscriptionId"],"visible":true},{"id":"col_2_58d73578492912c0bc3cdb747f10471b","label":"SubscriptionReferenceNumber","field":"autodesk_subscriptions_subscriptionReferenceNumber","filter":true,"fields":["autodesk_subscriptions_subscriptionReferenceNumber"],"visible":true},{"id":"col_3_106ba99b9133ffceb67190c32971f590","label":"Quantity","field":"autodesk_subscriptions_quantity","filter":true,"fields":["autodesk_subscriptions_quantity"],"visible":true},{"id":"col_4_ca291139f687964f3127db9f095a632f","label":"Status","field":"autodesk_subscriptions_status","filter":true,"fields":["autodesk_subscriptions_status"],"visible":true},{"id":"col_5_72042a18994c57e2550107169a75948b","label":"StartDate","field":"autodesk_subscriptions_startDate","filter":true,"fields":["autodesk_subscriptions_startDate"],"visible":true},{"id":"col_6_f659cbb18749c212f5d8d8394e6e6ae6","label":"EndDate","field":"autodesk_subscriptions_endDate","filter":true,"fields":["autodesk_subscriptions_endDate"],"visible":true},{"id":"col_7_9e1c960afc321c57dedd0fef78a44e1a","label":"PaymentMethod","field":"autodesk_subscriptions_paymentMethod","filter":true,"fields":["autodesk_subscriptions_paymentMethod"],"visible":true},{"id":"col_8_15c27ccc098b776b2e7455de1c4b48f1","label":"OfferingId","field":"autodesk_subscriptions_offeringId","filter":true,"fields":["autodesk_subscriptions_offeringId"],"visible":true},{"id":"col_9_e582871ab3e85044903aa525b5171672","label":"OfferingCode","field":"autodesk_subscriptions_offeringCode","filter":true,"fields":["autodesk_subscriptions_offeringCode"],"visible":true},{"id":"col_10_eb8f24d7ac24cbbede212ec13ad1a7a4","label":"OfferingName","field":"autodesk_subscriptions_offeringName","filter":true,"fields":["autodesk_subscriptions_offeringName"],"visible":true},{"id":"col_11_eb4fe489f6b9a02e0b0449d4a95189b2","label":"AutoRenew","field":"autodesk_subscriptions_autoRenew","filter":true,"fields":["autodesk_subscriptions_autoRenew"],"visible":true},{"id":"col_12_f50cc2aeff9e53f8234f369822642d19","label":"Id","field":"autodesk_accounts_id","filter":true,"fields":["autodesk_accounts_id"],"visible":true},{"id":"col_13_2f0806da78d8b405d7764781d98fccb1","label":"Account Csn","field":"autodesk_accounts_account_csn","filter":true,"fields":["autodesk_accounts_account_csn"],"visible":true},{"id":"col_14_c8f593147edff67116e1956639cba78f","label":"Name","field":"autodesk_accounts_name","filter":true,"fields":["autodesk_accounts_name"],"visible":true},{"id":"col_15_9e43e26ba7fae652c2a7f1e80fe9175a","label":"First Name","field":"autodesk_accounts_first_name","filter":true,"fields":["autodesk_accounts_first_name"],"visible":true},{"id":"col_16_24c4d728953e65dbcc64196818c44ecf","label":"Last Name","field":"autodesk_accounts_last_name","filter":true,"fields":["autodesk_accounts_last_name"],"visible":true},{"id":"col_17_8019496b939aeffb1f2379403cf1cd5b","label":"Email","field":"autodesk_accounts_email","filter":true,"fields":["autodesk_accounts_email"],"visible":true},{"id":"col_18_2a0c834a0387d4c9b646f62816753ba9","label":"City","field":"autodesk_accounts_city","filter":true,"fields":["autodesk_accounts_city"],"visible":true},{"id":"col_19_9e064ab727b9c48e971823ae1deb2234","label":"Postal Code","field":"autodesk_accounts_postal_code","filter":true,"fields":["autodesk_accounts_postal_code"],"visible":true}],"hidden":[],"columns":[{"id":"col_0_c68c54809e9acf125fe784d260a50197","label":"Id","field":"autodesk_subscriptions_id","filter":true,"fields":["autodesk_subscriptions_id"],"visible":true},{"id":"col_1_8b965a912aecb63854dab8c6266ce829","label":"SubscriptionId","field":"autodesk_subscriptions_subscriptionId","filter":true,"fields":["autodesk_subscriptions_subscriptionId"],"visible":true},{"id":"col_2_58d73578492912c0bc3cdb747f10471b","label":"SubscriptionReferenceNumber","field":"autodesk_subscriptions_subscriptionReferenceNumber","filter":true,"fields":["autodesk_subscriptions_subscriptionReferenceNumber"],"visible":true},{"id":"col_3_106ba99b9133ffceb67190c32971f590","label":"Quantity","field":"autodesk_subscriptions_quantity","filter":true,"fields":["autodesk_subscriptions_quantity"],"visible":true},{"id":"col_4_ca291139f687964f3127db9f095a632f","label":"Status","field":"autodesk_subscriptions_status","filter":true,"fields":["autodesk_subscriptions_status"],"visible":true},{"id":"col_5_72042a18994c57e2550107169a75948b","label":"StartDate","field":"autodesk_subscriptions_startDate","filter":true,"fields":["autodesk_subscriptions_startDate"],"visible":true},{"id":"col_6_f659cbb18749c212f5d8d8394e6e6ae6","label":"EndDate","field":"autodesk_subscriptions_endDate","filter":true,"fields":["autodesk_subscriptions_endDate"],"visible":true},{"id":"col_7_9e1c960afc321c57dedd0fef78a44e1a","label":"PaymentMethod","field":"autodesk_subscriptions_paymentMethod","filter":true,"fields":["autodesk_subscriptions_paymentMethod"],"visible":true},{"id":"col_8_15c27ccc098b776b2e7455de1c4b48f1","label":"OfferingId","field":"autodesk_subscriptions_offeringId","filter":true,"fields":["autodesk_subscriptions_offeringId"],"visible":true},{"id":"col_9_e582871ab3e85044903aa525b5171672","label":"OfferingCode","field":"autodesk_subscriptions_offeringCode","filter":true,"fields":["autodesk_subscriptions_offeringCode"],"visible":true},{"id":"col_10_eb8f24d7ac24cbbede212ec13ad1a7a4","label":"OfferingName","field":"autodesk_subscriptions_offeringName","filter":true,"fields":["autodesk_subscriptions_offeringName"],"visible":true},{"id":"col_11_eb4fe489f6b9a02e0b0449d4a95189b2","label":"AutoRenew","field":"autodesk_subscriptions_autoRenew","filter":true,"fields":["autodesk_subscriptions_autoRenew"],"visible":true},{"id":"col_12_f50cc2aeff9e53f8234f369822642d19","label":"Id","field":"autodesk_accounts_id","filter":true,"fields":["autodesk_accounts_id"],"visible":true},{"id":"col_13_2f0806da78d8b405d7764781d98fccb1","label":"Account Csn","field":"autodesk_accounts_account_csn","filter":true,"fields":["autodesk_accounts_account_csn"],"visible":true},{"id":"col_14_c8f593147edff67116e1956639cba78f","label":"Name","field":"autodesk_accounts_name","filter":true,"fields":["autodesk_accounts_name"],"visible":true},{"id":"col_15_9e43e26ba7fae652c2a7f1e80fe9175a","label":"First Name","field":"autodesk_accounts_first_name","filter":true,"fields":["autodesk_accounts_first_name"],"visible":true},{"id":"col_16_24c4d728953e65dbcc64196818c44ecf","label":"Last Name","field":"autodesk_accounts_last_name","filter":true,"fields":["autodesk_accounts_last_name"],"visible":true},{"id":"col_17_8019496b939aeffb1f2379403cf1cd5b","label":"Email","field":"autodesk_accounts_email","filter":true,"fields":["autodesk_accounts_email"],"visible":true},{"id":"col_18_2a0c834a0387d4c9b646f62816753ba9","label":"City","field":"autodesk_accounts_city","filter":true,"fields":["autodesk_accounts_city"],"visible":true},{"id":"col_19_9e064ab727b9c48e971823ae1deb2234","label":"Postal Code","field":"autodesk_accounts_postal_code","filter":true,"fields":["autodesk_accounts_postal_code"],"visible":true}]}"\n    [":data_source_id"]: int(1)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(86) "/baffletrain/autocadlt/autobooks/api/data_table_storage/update_data_source_and_columns"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1291) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(683): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(425): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(129): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table_storage.api.php(364): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(86): api\data_table_storage\update_data_source_and_columns()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '2' for key 'unique_table_user'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry '2' for key 'unique_table_user'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"",":user_id":2,":configuration":"{\"data_source_type\":\"data_source\",\"data_source_id\":1,\"updated_at\":\"2025-08-18 10:27:35\",\"structure\":[{\"id\":\"col_0_c68c54809e9acf125fe784d260a50197\",\"label\":\"Id\",\"field\":\"autodesk_subscriptions_id\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_id\"],\"visible\":true},{\"id\":\"col_1_8b965a912aecb63854dab8c6266ce829\",\"label\":\"SubscriptionId\",\"field\":\"autodesk_subscriptions_subscriptionId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionId\"],\"visible\":true},{\"id\":\"col_2_58d73578492912c0bc3cdb747f10471b\",\"label\":\"SubscriptionReferenceNumber\",\"field\":\"autodesk_subscriptions_subscriptionReferenceNumber\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionReferenceNumber\"],\"visible\":true},{\"id\":\"col_3_106ba99b9133ffceb67190c32971f590\",\"label\":\"Quantity\",\"field\":\"autodesk_subscriptions_quantity\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_quantity\"],\"visible\":true},{\"id\":\"col_4_ca291139f687964f3127db9f095a632f\",\"label\":\"Status\",\"field\":\"autodesk_subscriptions_status\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_status\"],\"visible\":true},{\"id\":\"col_5_72042a18994c57e2550107169a75948b\",\"label\":\"StartDate\",\"field\":\"autodesk_subscriptions_startDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_startDate\"],\"visible\":true},{\"id\":\"col_6_f659cbb18749c212f5d8d8394e6e6ae6\",\"label\":\"EndDate\",\"field\":\"autodesk_subscriptions_endDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_endDate\"],\"visible\":true},{\"id\":\"col_7_9e1c960afc321c57dedd0fef78a44e1a\",\"label\":\"PaymentMethod\",\"field\":\"autodesk_subscriptions_paymentMethod\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_paymentMethod\"],\"visible\":true},{\"id\":\"col_8_15c27ccc098b776b2e7455de1c4b48f1\",\"label\":\"OfferingId\",\"field\":\"autodesk_subscriptions_offeringId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringId\"],\"visible\":true},{\"id\":\"col_9_e582871ab3e85044903aa525b5171672\",\"label\":\"OfferingCode\",\"field\":\"autodesk_subscriptions_offeringCode\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringCode\"],\"visible\":true},{\"id\":\"col_10_eb8f24d7ac24cbbede212ec13ad1a7a4\",\"label\":\"OfferingName\",\"field\":\"autodesk_subscriptions_offeringName\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringName\"],\"visible\":true},{\"id\":\"col_11_eb4fe489f6b9a02e0b0449d4a95189b2\",\"label\":\"AutoRenew\",\"field\":\"autodesk_subscriptions_autoRenew\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_autoRenew\"],\"visible\":true},{\"id\":\"col_12_f50cc2aeff9e53f8234f369822642d19\",\"label\":\"Id\",\"field\":\"autodesk_accounts_id\",\"filter\":true,\"fields\":[\"autodesk_accounts_id\"],\"visible\":true},{\"id\":\"col_13_2f0806da78d8b405d7764781d98fccb1\",\"label\":\"Account Csn\",\"field\":\"autodesk_accounts_account_csn\",\"filter\":true,\"fields\":[\"autodesk_accounts_account_csn\"],\"visible\":true},{\"id\":\"col_14_c8f593147edff67116e1956639cba78f\",\"label\":\"Name\",\"field\":\"autodesk_accounts_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_name\"],\"visible\":true},{\"id\":\"col_15_9e43e26ba7fae652c2a7f1e80fe9175a\",\"label\":\"First Name\",\"field\":\"autodesk_accounts_first_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_first_name\"],\"visible\":true},{\"id\":\"col_16_24c4d728953e65dbcc64196818c44ecf\",\"label\":\"Last Name\",\"field\":\"autodesk_accounts_last_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_last_name\"],\"visible\":true},{\"id\":\"col_17_8019496b939aeffb1f2379403cf1cd5b\",\"label\":\"Email\",\"field\":\"autodesk_accounts_email\",\"filter\":true,\"fields\":[\"autodesk_accounts_email\"],\"visible\":true},{\"id\":\"col_18_2a0c834a0387d4c9b646f62816753ba9\",\"label\":\"City\",\"field\":\"autodesk_accounts_city\",\"filter\":true,\"fields\":[\"autodesk_accounts_city\"],\"visible\":true},{\"id\":\"col_19_9e064ab727b9c48e971823ae1deb2234\",\"label\":\"Postal Code\",\"field\":\"autodesk_accounts_postal_code\",\"filter\":true,\"fields\":[\"autodesk_accounts_postal_code\"],\"visible\":true}],\"hidden\":[],\"columns\":[{\"id\":\"col_0_c68c54809e9acf125fe784d260a50197\",\"label\":\"Id\",\"field\":\"autodesk_subscriptions_id\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_id\"],\"visible\":true},{\"id\":\"col_1_8b965a912aecb63854dab8c6266ce829\",\"label\":\"SubscriptionId\",\"field\":\"autodesk_subscriptions_subscriptionId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionId\"],\"visible\":true},{\"id\":\"col_2_58d73578492912c0bc3cdb747f10471b\",\"label\":\"SubscriptionReferenceNumber\",\"field\":\"autodesk_subscriptions_subscriptionReferenceNumber\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionReferenceNumber\"],\"visible\":true},{\"id\":\"col_3_106ba99b9133ffceb67190c32971f590\",\"label\":\"Quantity\",\"field\":\"autodesk_subscriptions_quantity\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_quantity\"],\"visible\":true},{\"id\":\"col_4_ca291139f687964f3127db9f095a632f\",\"label\":\"Status\",\"field\":\"autodesk_subscriptions_status\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_status\"],\"visible\":true},{\"id\":\"col_5_72042a18994c57e2550107169a75948b\",\"label\":\"StartDate\",\"field\":\"autodesk_subscriptions_startDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_startDate\"],\"visible\":true},{\"id\":\"col_6_f659cbb18749c212f5d8d8394e6e6ae6\",\"label\":\"EndDate\",\"field\":\"autodesk_subscriptions_endDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_endDate\"],\"visible\":true},{\"id\":\"col_7_9e1c960afc321c57dedd0fef78a44e1a\",\"label\":\"PaymentMethod\",\"field\":\"autodesk_subscriptions_paymentMethod\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_paymentMethod\"],\"visible\":true},{\"id\":\"col_8_15c27ccc098b776b2e7455de1c4b48f1\",\"label\":\"OfferingId\",\"field\":\"autodesk_subscriptions_offeringId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringId\"],\"visible\":true},{\"id\":\"col_9_e582871ab3e85044903aa525b5171672\",\"label\":\"OfferingCode\",\"field\":\"autodesk_subscriptions_offeringCode\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringCode\"],\"visible\":true},{\"id\":\"col_10_eb8f24d7ac24cbbede212ec13ad1a7a4\",\"label\":\"OfferingName\",\"field\":\"autodesk_subscriptions_offeringName\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringName\"],\"visible\":true},{\"id\":\"col_11_eb4fe489f6b9a02e0b0449d4a95189b2\",\"label\":\"AutoRenew\",\"field\":\"autodesk_subscriptions_autoRenew\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_autoRenew\"],\"visible\":true},{\"id\":\"col_12_f50cc2aeff9e53f8234f369822642d19\",\"label\":\"Id\",\"field\":\"autodesk_accounts_id\",\"filter\":true,\"fields\":[\"autodesk_accounts_id\"],\"visible\":true},{\"id\":\"col_13_2f0806da78d8b405d7764781d98fccb1\",\"label\":\"Account Csn\",\"field\":\"autodesk_accounts_account_csn\",\"filter\":true,\"fields\":[\"autodesk_accounts_account_csn\"],\"visible\":true},{\"id\":\"col_14_c8f593147edff67116e1956639cba78f\",\"label\":\"Name\",\"field\":\"autodesk_accounts_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_name\"],\"visible\":true},{\"id\":\"col_15_9e43e26ba7fae652c2a7f1e80fe9175a\",\"label\":\"First Name\",\"field\":\"autodesk_accounts_first_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_first_name\"],\"visible\":true},{\"id\":\"col_16_24c4d728953e65dbcc64196818c44ecf\",\"label\":\"Last Name\",\"field\":\"autodesk_accounts_last_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_last_name\"],\"visible\":true},{\"id\":\"col_17_8019496b939aeffb1f2379403cf1cd5b\",\"label\":\"Email\",\"field\":\"autodesk_accounts_email\",\"filter\":true,\"fields\":[\"autodesk_accounts_email\"],\"visible\":true},{\"id\":\"col_18_2a0c834a0387d4c9b646f62816753ba9\",\"label\":\"City\",\"field\":\"autodesk_accounts_city\",\"filter\":true,\"fields\":[\"autodesk_accounts_city\"],\"visible\":true},{\"id\":\"col_19_9e064ab727b9c48e971823ae1deb2234\",\"label\":\"Postal Code\",\"field\":\"autodesk_accounts_postal_code\",\"filter\":true,\"fields\":[\"autodesk_accounts_postal_code\"],\"visible\":true}]}",":data_source_id":1},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_table_storage\/update_data_source_and_columns","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(683): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(425): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(129): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table_storage.api.php(364): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(86): api\\data_table_storage\\update_data_source_and_columns()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry '2' for key 'unique_table_user'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"",":user_id":2,":configuration":"{\"data_source_type\":\"data_source\",\"data_source_id\":1,\"updated_at\":\"2025-08-18 10:27:35\",\"structure\":[{\"id\":\"col_0_c68c54809e9acf125fe784d260a50197\",\"label\":\"Id\",\"field\":\"autodesk_subscriptions_id\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_id\"],\"visible\":true},{\"id\":\"col_1_8b965a912aecb63854dab8c6266ce829\",\"label\":\"SubscriptionId\",\"field\":\"autodesk_subscriptions_subscriptionId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionId\"],\"visible\":true},{\"id\":\"col_2_58d73578492912c0bc3cdb747f10471b\",\"label\":\"SubscriptionReferenceNumber\",\"field\":\"autodesk_subscriptions_subscriptionReferenceNumber\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionReferenceNumber\"],\"visible\":true},{\"id\":\"col_3_106ba99b9133ffceb67190c32971f590\",\"label\":\"Quantity\",\"field\":\"autodesk_subscriptions_quantity\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_quantity\"],\"visible\":true},{\"id\":\"col_4_ca291139f687964f3127db9f095a632f\",\"label\":\"Status\",\"field\":\"autodesk_subscriptions_status\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_status\"],\"visible\":true},{\"id\":\"col_5_72042a18994c57e2550107169a75948b\",\"label\":\"StartDate\",\"field\":\"autodesk_subscriptions_startDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_startDate\"],\"visible\":true},{\"id\":\"col_6_f659cbb18749c212f5d8d8394e6e6ae6\",\"label\":\"EndDate\",\"field\":\"autodesk_subscriptions_endDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_endDate\"],\"visible\":true},{\"id\":\"col_7_9e1c960afc321c57dedd0fef78a44e1a\",\"label\":\"PaymentMethod\",\"field\":\"autodesk_subscriptions_paymentMethod\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_paymentMethod\"],\"visible\":true},{\"id\":\"col_8_15c27ccc098b776b2e7455de1c4b48f1\",\"label\":\"OfferingId\",\"field\":\"autodesk_subscriptions_offeringId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringId\"],\"visible\":true},{\"id\":\"col_9_e582871ab3e85044903aa525b5171672\",\"label\":\"OfferingCode\",\"field\":\"autodesk_subscriptions_offeringCode\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringCode\"],\"visible\":true},{\"id\":\"col_10_eb8f24d7ac24cbbede212ec13ad1a7a4\",\"label\":\"OfferingName\",\"field\":\"autodesk_subscriptions_offeringName\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringName\"],\"visible\":true},{\"id\":\"col_11_eb4fe489f6b9a02e0b0449d4a95189b2\",\"label\":\"AutoRenew\",\"field\":\"autodesk_subscriptions_autoRenew\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_autoRenew\"],\"visible\":true},{\"id\":\"col_12_f50cc2aeff9e53f8234f369822642d19\",\"label\":\"Id\",\"field\":\"autodesk_accounts_id\",\"filter\":true,\"fields\":[\"autodesk_accounts_id\"],\"visible\":true},{\"id\":\"col_13_2f0806da78d8b405d7764781d98fccb1\",\"label\":\"Account Csn\",\"field\":\"autodesk_accounts_account_csn\",\"filter\":true,\"fields\":[\"autodesk_accounts_account_csn\"],\"visible\":true},{\"id\":\"col_14_c8f593147edff67116e1956639cba78f\",\"label\":\"Name\",\"field\":\"autodesk_accounts_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_name\"],\"visible\":true},{\"id\":\"col_15_9e43e26ba7fae652c2a7f1e80fe9175a\",\"label\":\"First Name\",\"field\":\"autodesk_accounts_first_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_first_name\"],\"visible\":true},{\"id\":\"col_16_24c4d728953e65dbcc64196818c44ecf\",\"label\":\"Last Name\",\"field\":\"autodesk_accounts_last_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_last_name\"],\"visible\":true},{\"id\":\"col_17_8019496b939aeffb1f2379403cf1cd5b\",\"label\":\"Email\",\"field\":\"autodesk_accounts_email\",\"filter\":true,\"fields\":[\"autodesk_accounts_email\"],\"visible\":true},{\"id\":\"col_18_2a0c834a0387d4c9b646f62816753ba9\",\"label\":\"City\",\"field\":\"autodesk_accounts_city\",\"filter\":true,\"fields\":[\"autodesk_accounts_city\"],\"visible\":true},{\"id\":\"col_19_9e064ab727b9c48e971823ae1deb2234\",\"label\":\"Postal Code\",\"field\":\"autodesk_accounts_postal_code\",\"filter\":true,\"fields\":[\"autodesk_accounts_postal_code\"],\"visible\":true}],\"hidden\":[],\"columns\":[{\"id\":\"col_0_c68c54809e9acf125fe784d260a50197\",\"label\":\"Id\",\"field\":\"autodesk_subscriptions_id\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_id\"],\"visible\":true},{\"id\":\"col_1_8b965a912aecb63854dab8c6266ce829\",\"label\":\"SubscriptionId\",\"field\":\"autodesk_subscriptions_subscriptionId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionId\"],\"visible\":true},{\"id\":\"col_2_58d73578492912c0bc3cdb747f10471b\",\"label\":\"SubscriptionReferenceNumber\",\"field\":\"autodesk_subscriptions_subscriptionReferenceNumber\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionReferenceNumber\"],\"visible\":true},{\"id\":\"col_3_106ba99b9133ffceb67190c32971f590\",\"label\":\"Quantity\",\"field\":\"autodesk_subscriptions_quantity\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_quantity\"],\"visible\":true},{\"id\":\"col_4_ca291139f687964f3127db9f095a632f\",\"label\":\"Status\",\"field\":\"autodesk_subscriptions_status\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_status\"],\"visible\":true},{\"id\":\"col_5_72042a18994c57e2550107169a75948b\",\"label\":\"StartDate\",\"field\":\"autodesk_subscriptions_startDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_startDate\"],\"visible\":true},{\"id\":\"col_6_f659cbb18749c212f5d8d8394e6e6ae6\",\"label\":\"EndDate\",\"field\":\"autodesk_subscriptions_endDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_endDate\"],\"visible\":true},{\"id\":\"col_7_9e1c960afc321c57dedd0fef78a44e1a\",\"label\":\"PaymentMethod\",\"field\":\"autodesk_subscriptions_paymentMethod\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_paymentMethod\"],\"visible\":true},{\"id\":\"col_8_15c27ccc098b776b2e7455de1c4b48f1\",\"label\":\"OfferingId\",\"field\":\"autodesk_subscriptions_offeringId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringId\"],\"visible\":true},{\"id\":\"col_9_e582871ab3e85044903aa525b5171672\",\"label\":\"OfferingCode\",\"field\":\"autodesk_subscriptions_offeringCode\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringCode\"],\"visible\":true},{\"id\":\"col_10_eb8f24d7ac24cbbede212ec13ad1a7a4\",\"label\":\"OfferingName\",\"field\":\"autodesk_subscriptions_offeringName\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringName\"],\"visible\":true},{\"id\":\"col_11_eb4fe489f6b9a02e0b0449d4a95189b2\",\"label\":\"AutoRenew\",\"field\":\"autodesk_subscriptions_autoRenew\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_autoRenew\"],\"visible\":true},{\"id\":\"col_12_f50cc2aeff9e53f8234f369822642d19\",\"label\":\"Id\",\"field\":\"autodesk_accounts_id\",\"filter\":true,\"fields\":[\"autodesk_accounts_id\"],\"visible\":true},{\"id\":\"col_13_2f0806da78d8b405d7764781d98fccb1\",\"label\":\"Account Csn\",\"field\":\"autodesk_accounts_account_csn\",\"filter\":true,\"fields\":[\"autodesk_accounts_account_csn\"],\"visible\":true},{\"id\":\"col_14_c8f593147edff67116e1956639cba78f\",\"label\":\"Name\",\"field\":\"autodesk_accounts_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_name\"],\"visible\":true},{\"id\":\"col_15_9e43e26ba7fae652c2a7f1e80fe9175a\",\"label\":\"First Name\",\"field\":\"autodesk_accounts_first_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_first_name\"],\"visible\":true},{\"id\":\"col_16_24c4d728953e65dbcc64196818c44ecf\",\"label\":\"Last Name\",\"field\":\"autodesk_accounts_last_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_last_name\"],\"visible\":true},{\"id\":\"col_17_8019496b939aeffb1f2379403cf1cd5b\",\"label\":\"Email\",\"field\":\"autodesk_accounts_email\",\"filter\":true,\"fields\":[\"autodesk_accounts_email\"],\"visible\":true},{\"id\":\"col_18_2a0c834a0387d4c9b646f62816753ba9\",\"label\":\"City\",\"field\":\"autodesk_accounts_city\",\"filter\":true,\"fields\":[\"autodesk_accounts_city\"],\"visible\":true},{\"id\":\"col_19_9e064ab727b9c48e971823ae1deb2234\",\"label\":\"Postal Code\",\"field\":\"autodesk_accounts_postal_code\",\"filter\":true,\"fields\":[\"autodesk_accounts_postal_code\"],\"visible\":true}]}",":data_source_id":1}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 425\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"",":user_id":2,":configuration":"{\"data_source_type\":\"data_source\",\"data_source_id\":1,\"updated_at\":\"2025-08-18 10:27:35\",\"structure\":[{\"id\":\"col_0_c68c54809e9acf125fe784d260a50197\",\"label\":\"Id\",\"field\":\"autodesk_subscriptions_id\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_id\"],\"visible\":true},{\"id\":\"col_1_8b965a912aecb63854dab8c6266ce829\",\"label\":\"SubscriptionId\",\"field\":\"autodesk_subscriptions_subscriptionId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionId\"],\"visible\":true},{\"id\":\"col_2_58d73578492912c0bc3cdb747f10471b\",\"label\":\"SubscriptionReferenceNumber\",\"field\":\"autodesk_subscriptions_subscriptionReferenceNumber\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionReferenceNumber\"],\"visible\":true},{\"id\":\"col_3_106ba99b9133ffceb67190c32971f590\",\"label\":\"Quantity\",\"field\":\"autodesk_subscriptions_quantity\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_quantity\"],\"visible\":true},{\"id\":\"col_4_ca291139f687964f3127db9f095a632f\",\"label\":\"Status\",\"field\":\"autodesk_subscriptions_status\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_status\"],\"visible\":true},{\"id\":\"col_5_72042a18994c57e2550107169a75948b\",\"label\":\"StartDate\",\"field\":\"autodesk_subscriptions_startDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_startDate\"],\"visible\":true},{\"id\":\"col_6_f659cbb18749c212f5d8d8394e6e6ae6\",\"label\":\"EndDate\",\"field\":\"autodesk_subscriptions_endDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_endDate\"],\"visible\":true},{\"id\":\"col_7_9e1c960afc321c57dedd0fef78a44e1a\",\"label\":\"PaymentMethod\",\"field\":\"autodesk_subscriptions_paymentMethod\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_paymentMethod\"],\"visible\":true},{\"id\":\"col_8_15c27ccc098b776b2e7455de1c4b48f1\",\"label\":\"OfferingId\",\"field\":\"autodesk_subscriptions_offeringId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringId\"],\"visible\":true},{\"id\":\"col_9_e582871ab3e85044903aa525b5171672\",\"label\":\"OfferingCode\",\"field\":\"autodesk_subscriptions_offeringCode\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringCode\"],\"visible\":true},{\"id\":\"col_10_eb8f24d7ac24cbbede212ec13ad1a7a4\",\"label\":\"OfferingName\",\"field\":\"autodesk_subscriptions_offeringName\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringName\"],\"visible\":true},{\"id\":\"col_11_eb4fe489f6b9a02e0b0449d4a95189b2\",\"label\":\"AutoRenew\",\"field\":\"autodesk_subscriptions_autoRenew\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_autoRenew\"],\"visible\":true},{\"id\":\"col_12_f50cc2aeff9e53f8234f369822642d19\",\"label\":\"Id\",\"field\":\"autodesk_accounts_id\",\"filter\":true,\"fields\":[\"autodesk_accounts_id\"],\"visible\":true},{\"id\":\"col_13_2f0806da78d8b405d7764781d98fccb1\",\"label\":\"Account Csn\",\"field\":\"autodesk_accounts_account_csn\",\"filter\":true,\"fields\":[\"autodesk_accounts_account_csn\"],\"visible\":true},{\"id\":\"col_14_c8f593147edff67116e1956639cba78f\",\"label\":\"Name\",\"field\":\"autodesk_accounts_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_name\"],\"visible\":true},{\"id\":\"col_15_9e43e26ba7fae652c2a7f1e80fe9175a\",\"label\":\"First Name\",\"field\":\"autodesk_accounts_first_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_first_name\"],\"visible\":true},{\"id\":\"col_16_24c4d728953e65dbcc64196818c44ecf\",\"label\":\"Last Name\",\"field\":\"autodesk_accounts_last_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_last_name\"],\"visible\":true},{\"id\":\"col_17_8019496b939aeffb1f2379403cf1cd5b\",\"label\":\"Email\",\"field\":\"autodesk_accounts_email\",\"filter\":true,\"fields\":[\"autodesk_accounts_email\"],\"visible\":true},{\"id\":\"col_18_2a0c834a0387d4c9b646f62816753ba9\",\"label\":\"City\",\"field\":\"autodesk_accounts_city\",\"filter\":true,\"fields\":[\"autodesk_accounts_city\"],\"visible\":true},{\"id\":\"col_19_9e064ab727b9c48e971823ae1deb2234\",\"label\":\"Postal Code\",\"field\":\"autodesk_accounts_postal_code\",\"filter\":true,\"fields\":[\"autodesk_accounts_postal_code\"],\"visible\":true}],\"hidden\":[],\"columns\":[{\"id\":\"col_0_c68c54809e9acf125fe784d260a50197\",\"label\":\"Id\",\"field\":\"autodesk_subscriptions_id\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_id\"],\"visible\":true},{\"id\":\"col_1_8b965a912aecb63854dab8c6266ce829\",\"label\":\"SubscriptionId\",\"field\":\"autodesk_subscriptions_subscriptionId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionId\"],\"visible\":true},{\"id\":\"col_2_58d73578492912c0bc3cdb747f10471b\",\"label\":\"SubscriptionReferenceNumber\",\"field\":\"autodesk_subscriptions_subscriptionReferenceNumber\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_subscriptionReferenceNumber\"],\"visible\":true},{\"id\":\"col_3_106ba99b9133ffceb67190c32971f590\",\"label\":\"Quantity\",\"field\":\"autodesk_subscriptions_quantity\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_quantity\"],\"visible\":true},{\"id\":\"col_4_ca291139f687964f3127db9f095a632f\",\"label\":\"Status\",\"field\":\"autodesk_subscriptions_status\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_status\"],\"visible\":true},{\"id\":\"col_5_72042a18994c57e2550107169a75948b\",\"label\":\"StartDate\",\"field\":\"autodesk_subscriptions_startDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_startDate\"],\"visible\":true},{\"id\":\"col_6_f659cbb18749c212f5d8d8394e6e6ae6\",\"label\":\"EndDate\",\"field\":\"autodesk_subscriptions_endDate\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_endDate\"],\"visible\":true},{\"id\":\"col_7_9e1c960afc321c57dedd0fef78a44e1a\",\"label\":\"PaymentMethod\",\"field\":\"autodesk_subscriptions_paymentMethod\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_paymentMethod\"],\"visible\":true},{\"id\":\"col_8_15c27ccc098b776b2e7455de1c4b48f1\",\"label\":\"OfferingId\",\"field\":\"autodesk_subscriptions_offeringId\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringId\"],\"visible\":true},{\"id\":\"col_9_e582871ab3e85044903aa525b5171672\",\"label\":\"OfferingCode\",\"field\":\"autodesk_subscriptions_offeringCode\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringCode\"],\"visible\":true},{\"id\":\"col_10_eb8f24d7ac24cbbede212ec13ad1a7a4\",\"label\":\"OfferingName\",\"field\":\"autodesk_subscriptions_offeringName\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_offeringName\"],\"visible\":true},{\"id\":\"col_11_eb4fe489f6b9a02e0b0449d4a95189b2\",\"label\":\"AutoRenew\",\"field\":\"autodesk_subscriptions_autoRenew\",\"filter\":true,\"fields\":[\"autodesk_subscriptions_autoRenew\"],\"visible\":true},{\"id\":\"col_12_f50cc2aeff9e53f8234f369822642d19\",\"label\":\"Id\",\"field\":\"autodesk_accounts_id\",\"filter\":true,\"fields\":[\"autodesk_accounts_id\"],\"visible\":true},{\"id\":\"col_13_2f0806da78d8b405d7764781d98fccb1\",\"label\":\"Account Csn\",\"field\":\"autodesk_accounts_account_csn\",\"filter\":true,\"fields\":[\"autodesk_accounts_account_csn\"],\"visible\":true},{\"id\":\"col_14_c8f593147edff67116e1956639cba78f\",\"label\":\"Name\",\"field\":\"autodesk_accounts_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_name\"],\"visible\":true},{\"id\":\"col_15_9e43e26ba7fae652c2a7f1e80fe9175a\",\"label\":\"First Name\",\"field\":\"autodesk_accounts_first_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_first_name\"],\"visible\":true},{\"id\":\"col_16_24c4d728953e65dbcc64196818c44ecf\",\"label\":\"Last Name\",\"field\":\"autodesk_accounts_last_name\",\"filter\":true,\"fields\":[\"autodesk_accounts_last_name\"],\"visible\":true},{\"id\":\"col_17_8019496b939aeffb1f2379403cf1cd5b\",\"label\":\"Email\",\"field\":\"autodesk_accounts_email\",\"filter\":true,\"fields\":[\"autodesk_accounts_email\"],\"visible\":true},{\"id\":\"col_18_2a0c834a0387d4c9b646f62816753ba9\",\"label\":\"City\",\"field\":\"autodesk_accounts_city\",\"filter\":true,\"fields\":[\"autodesk_accounts_city\"],\"visible\":true},{\"id\":\"col_19_9e064ab727b9c48e971823ae1deb2234\",\"label\":\"Postal Code\",\"field\":\"autodesk_accounts_postal_code\",\"filter\":true,\"fields\":[\"autodesk_accounts_postal_code\"],\"visible\":true}]}",":data_source_id":1}\n-->\n
[database_errors] [2025-08-18 21:30:06] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "HY093"\n  ["error_message"]: string(68) "SQLSTATE[HY093]: Invalid parameter number: parameter was not defined"\n  ["sql_state"]: string(5) "HY093"\n  ["driver_error_code"]: int(0)\n  ["driver_error_message"]: string(7) "Unknown"\n  ["query"]: string(190) "SELECT * FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 AND (`table_name` = :nested_1_where_table_name_0 OR `data_source_id` = :nested_1_where_data_source_id_1) LIMIT 1"\n  ["parameters"]: array(3) {\n    [":where_user_id_0"]: int(2)\n    ["nested_1_:where_table_name_0"]: string(17) "autodesk_products"\n    ["nested_1_:where_data_source_id_1"]: int(63)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(86) "/baffletrain/autocadlt/autobooks/api/data_table_storage/update_data_source_and_columns"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/******** Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1432) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(683): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(260): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(291): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(102): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table_storage.api.php(447): system\data_table_storage::save_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(86): api\data_table_storage\update_data_source_and_columns()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#10 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"HY093","error_message":"SQLSTATE[HY093]: Invalid parameter number: parameter was not defined","sql_state":"HY093","driver_error_code":0,"driver_error_message":"Unknown","query":"SELECT * FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 AND (`table_name` = :nested_1_where_table_name_0 OR `data_source_id` = :nested_1_where_data_source_id_1) LIMIT 1","parameters":{":where_user_id_0":2,"nested_1_:where_table_name_0":"autodesk_products","nested_1_:where_data_source_id_1":63},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_table_storage\/update_data_source_and_columns","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/******** Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(683): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(260): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(291): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(102): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table_storage.api.php(447): system\\data_table_storage::save_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(86): api\\data_table_storage\\update_data_source_and_columns()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#10 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["HY093",0]}\n         1: "SELECT * FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 AND (`table_name` = :nested_1_where_table_name_0 OR `data_source_id` = :nested_1_where_data_source_id_1) LIMIT 1"\n         2: {":where_user_id_0":2,"nested_1_:where_table_name_0":"autodesk_products","nested_1_:where_data_source_id_1":63}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 260\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_data_table_storage WHERE `user_id` = :where_user_id_0 AND (`table_name` = :nested_1_where_table_name_0 OR `data_source_id` = :nested_1_where_data_source_id_1) LIMIT 1"\n         1: {":where_user_id_0":2,"nested_1_:where_table_name_0":"autodesk_products","nested_1_:where_data_source_id_1":63}\n-->\n
